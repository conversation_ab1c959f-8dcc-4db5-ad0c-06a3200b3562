import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductAttributeName } from 'src/database/entities/product/product-attribute-name.entity';
import {
  Between,
  EntityManager,
  In,
  Repository,
  Like,
  Or,
  And,
  LessThanOrEqual,
  Less<PERSON>han,
  Not,
  FindManyOptions,
  Equal,
  QueryRunner,
} from 'typeorm';
import {
  CreateCatalogProductAttributesDto,
  CreateCatalogProductDto,
  UpdateCatalogProductDto,
  UpdateCatalogProductAttributesDto,
  CreateAttributesGroupDto,
  UpdateAttributesGroupDto,
  CreateAttributesGroupRelationDto,
  GroupTierPriceDto,
} from './dto/catalog-product.dto';
import * as csvParser from 'csv-parser';
import * as fs from 'fs';
import * as path from 'path';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { FileInterceptor } from '@nestjs/platform-express';
import { AttributesGroup } from 'src/database/entities/product/attributes-group.entity';
import { AttributesGroupRelation } from 'src/database/entities/product/attributes-group-relation.entity';
import { S3Service } from 'src/utils/s3service';
import { json } from 'stream/consumers';
import { ProductAttributesOptions } from 'src/database/entities/product/attribute-options.entity';
import { IntegerAttributeValues } from 'src/database/entities/product/integer-attribute-values.entity';
import { Integer } from 'aws-sdk/clients/apigateway';
import { StringAttributeValues } from 'src/database/entities/product/string-attibute-values.entity';
import { TextAttributeValues } from 'src/database/entities/product/text-attribute-values.entity';
import { BooleanAttributeValues } from 'src/database/entities/product/boolean-attribute-values.entity';
import { DecimalAttributeValues } from 'src/database/entities/product/decimal-attribute-values.entity';
import { DateAttributeValues } from 'src/database/entities/product/date-attribute-values.entity';
import { TierPrices } from 'src/database/entities/product/tier-prices.entity';
import { InventoryAttributes } from 'src/database/entities/product/inventory-attributes-values.entity';
import { CatalogProductRelation } from 'src/database/entities/product/catalog-product-relation.entity';
import { CatalogCategory } from 'src/database/entities/category/main-category.entity';
import { ProductCategoryRelation } from 'src/database/entities/category/product-category-relation.entity';
import env from 'src/config/env';
import axios from 'axios';
import { modifySqlDataForElasticSearch } from 'src/utils/modifySqlDataForElasticSearch';
import { MediaGallary } from 'src/database/entities/product/media-gallary.entity';
import { MediaGallaryVideo } from 'src/database/entities/product/media-gallary-video.entity';
import { LoggerService } from 'src/utils/logger-service';
import { CatalogProductFlat } from 'src/database/entities/product/main-product-flat.entity';
import {
  dateInBasicFormat,
  formatDateString,
} from 'src/utils/curentDateAndTime';
import {
  CsvGenerateStatus,
  ProductDataCsvUrls,
} from 'src/database/entities/product/product-data-csv-urls.entity';
import {
  AttributesUpdateStatus,
  BulkAttributesUpdateStatus,
} from 'src/database/entities/product/bulk-attributes-update-status.entity';
import {
  Activity,
  EntityTypeEnum,
} from 'src/database/entities/product/activity.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';
import {
  EntityType,
  EventsOutbox,
} from 'src/database/entities/outbox/event-outbox.entity';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import { UrlRewrites } from 'src/database/entities/product/url-rewrites.entity';
import { SkuUpdateRecord } from 'src/database/entities/product/sku-update-record.entity';
import { FilterProductsDto } from './dto/filter-product.dto';
import { ProductStockAlert } from 'src/database/entities/product/product-stock-alert.entity';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
// Keep original imports
import { EventsLogService } from '../../../utils/events-logging-service';

enum SavingAction {
  CREATE = 'create',
  UPDATE = 'update',
}

enum AttributesValidationTypes {
  INT = 'int',
  VARCHAR = 'varchar',
  DECIMAL = 'decimal',
  DATETIME = 'datetime',
  TEXT = 'text',
  BOOLEAN = 'boolean',
  SELECT = 'select',
}

interface GroupedProducts {
  [price: number]: { price_group: number; child_ids: number[] };
}

@Injectable()
export class CatalogProductService {
  constructor(
    private readonly s3Service: S3Service,
    @InjectRepository(ProductAttributeName)
    private readonly productAttributesNameRepository: Repository<ProductAttributeName>,
    @InjectRepository(CatalogProduct)
    private readonly catalogProductRepository: Repository<CatalogProduct>,
    @InjectRepository(AttributesGroup)
    private readonly attributesGroupRepository: Repository<AttributesGroup>,
    @InjectRepository(AttributesGroupRelation)
    private readonly attributesGroupRelationRepository: Repository<AttributesGroupRelation>,
    @InjectRepository(ProductAttributesOptions)
    private readonly productAttributeOptionsRepository: Repository<ProductAttributesOptions>,
    @InjectRepository(IntegerAttributeValues)
    private readonly integerAttributeValuesRepository: Repository<IntegerAttributeValues>,
    @InjectRepository(StringAttributeValues)
    private readonly stringAttributeValuesRepository: Repository<StringAttributeValues>,
    @InjectRepository(TextAttributeValues)
    private readonly textAttributeValuesRepository: Repository<TextAttributeValues>,
    @InjectRepository(BooleanAttributeValues)
    private readonly booleanAttributeValuesRepository: Repository<BooleanAttributeValues>,
    @InjectRepository(DecimalAttributeValues)
    private readonly decimalAttributeValuesRepository: Repository<DecimalAttributeValues>,
    @InjectRepository(DateAttributeValues)
    private readonly dateAttributeValuesRepository: Repository<DateAttributeValues>,
    @InjectRepository(TierPrices)
    private readonly tierPricesRepository: Repository<TierPrices>,
    @InjectRepository(InventoryAttributes)
    private readonly inventoryAttributesRepository: Repository<InventoryAttributes>,
    @InjectRepository(CatalogProductRelation)
    private readonly catalogProductRelationsRepository: Repository<CatalogProductRelation>,
    @InjectRepository(CatalogCategory)
    private readonly catalogCategoryRepository: Repository<CatalogCategory>,
    @InjectRepository(MediaGallary)
    private readonly mediaGallaryRepository: Repository<MediaGallary>,
    @InjectRepository(MediaGallaryVideo)
    private readonly mediaGallaryVideoRepository: Repository<MediaGallaryVideo>,
    @InjectRepository(ProductCategoryRelation)
    private readonly productCategoryRelationRepository: Repository<ProductCategoryRelation>,
    @InjectRepository(CatalogProductFlat)
    private readonly catalogProductFlatRepository: Repository<CatalogProductFlat>,
    @InjectRepository(ProductDataCsvUrls)
    private readonly productDataCsvUrlsRepository: Repository<ProductDataCsvUrls>,
    @InjectRepository(BulkAttributesUpdateStatus)
    private readonly bulkAttributesUpdateRepository: Repository<BulkAttributesUpdateStatus>,
    @InjectRepository(Activity)
    private readonly activityRepository: Repository<Activity>,
    @InjectRepository(ActivityLogs)
    private readonly activityLogsRepository: Repository<ActivityLogs>,
    @InjectRepository(EventsOutbox)
    private readonly eventsOutboxRepository: Repository<EventsOutbox>,
    @InjectRepository(UrlRewrites)
    private readonly urlRewritesRepository: Repository<UrlRewrites>,
    @InjectRepository(SkuUpdateRecord)
    private readonly skuUpdateRecordRepository: Repository<SkuUpdateRecord>,
    @InjectRepository(ProductStockAlert)
    private readonly productStockAlertRepository: Repository<ProductStockAlert>,
    @InjectRepository(QuestionAnswer)
    private readonly productFaqRepository: Repository<QuestionAnswer>,
    private readonly entityManager: EntityManager,
    private readonly logger: LoggerService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly eventsLogService: EventsLogService,
  ) {}

  async addProductAttributes(body: CreateCatalogProductAttributesDto) {
    try {
      const { id, code } = body;
      const existingProductAttribute = await this.productAttributesFindOneBy({
        code,
      });

      if (existingProductAttribute) {
        this.logger.log('Attribute with this name already exists!');
        throw new BadRequestException(
          'attribute with this name already exists!',
        );
      }

      delete body.id;

      const createdProductAttribute =
        this.productAttributesNameRepository.create(body);

      await this.productAttributesNameRepository.save(createdProductAttribute);

      return createdProductAttribute;
    } catch (error) {
      this.logger.error('Failed to add product attributes', error);
      throw new BadRequestException('Failed to add product attributes');
    }
  }

  async getAllProductAttributes() {
    try {
      const allProductAttributes = await this.productAttributesFind();

      return allProductAttributes;
    } catch (error) {
      this.logger.error('Failed to retrieve product attributes', error);
      throw new InternalServerErrorException(
        'Failed to retrieve product attributes',
      );
    }
  }

  async updateProductAttribute(body: UpdateCatalogProductAttributesDto) {
    try {
      const existingProductAttribute = await this.productAttributesFindOneBy({
        id: body.id,
      });

      if (!existingProductAttribute)
        throw new BadRequestException('Unable to edit this product attribute');

      const updatedProductAttribute =
        await this.productAttributesNameRepository.save({
          ...existingProductAttribute,
          ...body,
        });

      return updatedProductAttribute;
    } catch (err) {
      this.logger.error('Failed to retrieve product attributes', err);
      throw new BadRequestException('Failed to update Product Attributes');
    }
  }

  async deleteProductAttribute(id: number) {
    try {
      const productAttribute = await this.productAttributesFindOneBy({ id });

      if (!productAttribute) {
        throw new BadRequestException('Product attribute not found');
      }

      const attributeRepositories = {
        varchar: this.stringAttributeValuesRepository,
        text: this.textAttributeValuesRepository,
        int: this.integerAttributeValuesRepository,
        decimal: this.decimalAttributeValuesRepository,
        boolean: this.booleanAttributeValuesRepository,
        datetime: this.dateAttributeValuesRepository,
      };

      const findProduct = await attributeRepositories[
        productAttribute.backend_type
      ].findOne({
        where: { attribute: { id } },
      });

      if (findProduct) {
        throw new BadRequestException(
          "Attribute is already attached to a product and can't be deleted",
        );
      }

      await this.productAttributesNameRepository.delete(id);

      return { id, response: 'Attribute deleted successfully' };
    } catch (err) {
      this.logger.error('Failed to delete product attribute', err);
      throw new InternalServerErrorException(
        'Failed to delete product attribute',
      );
    }
  }

  async createCatalogProduct(body: CreateCatalogProductDto, headers) {
    let queryRunner;
    console.log({ body });
    try {
      const connection = this.entityManager.connection;
      queryRunner = connection.createQueryRunner();

      let {
        id,
        // sku,
        status,
        type_id,
        tier_prices,
        inventory_details,
        product_links,
        category_associated,
      } = body;

      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'create',
        user_meta_info: user_data,
      };

      if (typeof inventory_details === 'undefined') {
        inventory_details = {
          is_in_stock: false,
          qty: 0,
          min_sale_qty: 1,
          max_sale_qty: 100,
          backorders: false,
        };
      }

      const flatAttributes: any = body.attributes_list.reduce(
        (acc, { attribute_code, value }) => {
          acc[attribute_code] = value;
          return acc;
        },
        {},
      );

      console.log('flatAttributes', flatAttributes);

      let productName = flatAttributes.name;
      let manufacturerValue = flatAttributes.manufacturer;
      let visibilityValue = flatAttributes.visibility;
      let mrpValue = flatAttributes.price;
      let sellingPriceValue = flatAttributes.special_price || flatAttributes.price;


      if (!productName || !type_id || !visibilityValue) {
        throw new BadRequestException(
          'Product cannot be created without name, typeid and visibility',
        );
      }

      if (productName === null || productName?.trim() === '') {
        throw new BadRequestException('Product name cannot be empty');
      }

      if (type_id === 'simple' && !manufacturerValue) {
        throw new BadRequestException(
          'Simple product cannot be created without manufacturer',
        );
      }

      if (Number(sellingPriceValue) > Number(mrpValue)) {
        throw new BadRequestException(
          'Selling price cannot be greater than MRP',
        );
      }

      if (!flatAttributes.weight) {
        body.attributes_list.push({
          attribute_code: 'weight',
          value: 100,
          enableRedirect: false,
        });
      }

      if (!flatAttributes.return_period) {
        body.attributes_list.push({
          attribute_code: 'return_period',
          value: 10,
          enableRedirect: false,
        });
      }

      let attributeOptionsValueArray = [];
      attributeOptionsValueArray.push('visibility');

      flatAttributes.manufacturer &&
        attributeOptionsValueArray.push('manufacturer');

      flatAttributes.tax_class_id &&
        attributeOptionsValueArray.push('tax_class_id');

      flatAttributes.dispatch_days &&
        attributeOptionsValueArray.push('dispatch_days');

      const existingOptionsForAttributesWithOptions =
        await this.productAttributeOptionsRepository.find({
          where: { attribute: { code: In(attributeOptionsValueArray) } },
          relations: ['attribute'],
          select: {
            id: true,
            value: true,
            attribute: {
              id: true,
              code: true,
            },
          },
        });

      const attributeOptionMap = new Map();

      existingOptionsForAttributesWithOptions.forEach((option) => {
        const attributeCode = option.attribute.code;
        if (!attributeOptionMap.has(attributeCode)) {
          attributeOptionMap.set(attributeCode, new Map());
        }
        attributeOptionMap.get(attributeCode).set(option.id, option);
      });


      flatAttributes.visibility = Number(flatAttributes.visibility);

      if (
        flatAttributes.visibility &&
        attributeOptionMap.get('visibility')?.get(flatAttributes.visibility) ==
          undefined
      ) {
        throw new BadRequestException('Invalid value provided for visibility');
      }

      if (
        flatAttributes.manufacturer &&
        attributeOptionMap
          .get('manufacturer')
          ?.get(flatAttributes.manufacturer) == undefined
      ) {
        throw new BadRequestException(
          'Invalid value provided for manufacturer',
        );
      }

      if (
        flatAttributes.dispatch_days &&
        attributeOptionMap
          .get('dispatch_days')
          ?.get(flatAttributes.dispatch_days) == undefined
      ) {
        throw new BadRequestException(
          'Invalid value provided for dispatch_days',
        );
      }

      if (
        flatAttributes.tax_class_id &&
        attributeOptionMap
          .get('tax_class_id')
          ?.get(flatAttributes.tax_class_id) == undefined
      ) {
        throw new BadRequestException(
          'Invalid value provided for tax_class_id',
        );
      }

      let generateSKU = await this.buildSku(body);

      let productBody = {
        id,
        sku: generateSKU,
        status,
        type_id,
      };

      let activityData, activityLogsData;
      let url_key_for_url_rewrites;

      let urlKeyAttribute = body.attributes_list.find(
        (attribute) => attribute.attribute_code === 'url_key',
      );
      if (urlKeyAttribute) {
        urlKeyAttribute.value = urlKeyAttribute.value
          .trim()
          .replace(/\s+/g, '-');

        const existing_url_key =
          await this.stringAttributeValuesRepository.findOne({
            where: {
              value: urlKeyAttribute.value,
              attribute: { code: 'url_key' },
            },
            relations: ['attribute'],
          });

        urlKeyAttribute = urlKeyAttribute.value.trim().replace(/\s+/g, '-');

        const existingCategoryUrlInUrlRewrites =
          await this.urlRewritesRepository.findOne({
            where: { request_path: `${urlKeyAttribute}.html` },
          });

        if (existing_url_key || existingCategoryUrlInUrlRewrites) {
          throw new BadRequestException('Provided url_key already exists');
        } else {
          url_key_for_url_rewrites = urlKeyAttribute;
        }
      } else {
        const existingCategoryUrlInUrlRewrites =
          await this.urlRewritesRepository.findOne({
            where: { request_path: `${productName}.html` },
          });
        if (existingCategoryUrlInUrlRewrites) {
          throw new BadRequestException(
            'Provided url_key already exists, please change product name',
          );
        }

        let generateUrl_key = await this.buildUrlKey(productName);

        body.attributes_list.push({
          attribute_code: 'url_key',
          value: generateUrl_key,
          enableRedirect: false,
        });

        // const url_key_attribute =
        //   await this.productAttributesNameRepository.findOne({
        //     where: { code: 'url_key' },
        //   });

        // const newAttribute = new StringAttributeValues();
        // newAttribute.attribute = url_key_attribute;
        // newAttribute.value = generateUrl_key;
        // newAttribute.product = createdProduct;

        // promisesArray.push(
        //   queryRunner.manager.save(StringAttributeValues, newAttribute),
        // );

        url_key_for_url_rewrites = generateUrl_key;
      }

      await queryRunner.connect();
      await queryRunner.startTransaction();

      // const createdProduct = connection
      //   .getRepository(CatalogProduct)
      //   .create(productBody);

      const createdProduct = queryRunner.manager.create(
        CatalogProduct,
        productBody,
      );

      await queryRunner.manager.save(CatalogProduct, createdProduct);

      console.timeEnd('Product id created');

      let createActivityLogsBody = {
        entity_id: createdProduct.id,
        old_value: null,
        new_value: null,
      };

      let url_rewrites_body = {
        entity_id: createdProduct.id,
        entity_type: EntityTypeEnum.PRODUCT,
        request_path: `${url_key_for_url_rewrites}.html`,
        target_path: String(createdProduct.id),
      };

      let promisesArray = [];

      const { createdUrlRewrite } = await this.modifyUrlRewritesV2(
        url_rewrites_body,
        queryRunner,
      );

      if (inventory_details) {
        promisesArray.push(
          this.saveInventoryData(
            inventory_details,
            createdProduct,
            queryRunner,
          ),
        );
      }

      if (product_links) {
        promisesArray.push(
          this.saveProductLinksData(product_links, createdProduct, queryRunner),
        );
      }

      if (tier_prices && createdProduct.type_id !== 'grouped')
        promisesArray.push(
          this.saveTierPrices(tier_prices, createdProduct, queryRunner),
        );

      if (body.attributes_list) {
        promisesArray.push(
          this.saveProductAttributesInRespectiveTables(
            body.attributes_list,
            createdProduct,
            { saveAction: SavingAction.CREATE },
            queryRunner,
          ),
        );
      }

      if (category_associated) {
        promisesArray.push(
          this.saveProductCategoryRelation(
            category_associated,
            createdProduct,
            queryRunner,
          ),
        );
      }

      // console.time('PROMISE.ALL');

      await Promise.all(promisesArray);

      // generateUrl_key = resolvedpromises[resolvedpromises.length - 1];

      // console.timeEnd('PROMISE.ALL');

      (activityData = await this.createActivity(createActivityBody)),
        (activityLogsData = await this.createActivityLog(
          {
            ...createActivityLogsBody,
            activity: activityData,
          },
          queryRunner,
        )),
        await Promise.all([
          this.eventsLogService.saveActivityAndEvent({
            headers,
            entityId: createdUrlRewrite.id,
            entityType: EntityType.URL_REWRITE,
            activityEntityType: EntityTypeEnum.URL_REWRITE,
            activityType: 'create-product-url-rewrite',
            queryRunner: queryRunner,
            updatedValue: null,
            previousValue: null,
          }),
          this.createEventOutbox(
            {
              entity_id: createdProduct.id,
              entity_type: EntityType.PRODUCT,
              activity_logs: activityLogsData,
            },
            queryRunner,
          ),
        ]);

      // await Promise.all([
      //   this.createEventOutbox(
      //     {
      //       entity_id: createdProduct.id,
      //       entity_type: EntityType.PRODUCT,
      //       activity_logs: activityLogsData,
      //     },
      //     queryRunner,
      //   ),
      // ]);

      await this.saveProductInFlatTable(
        createdProduct,
        body.attributes_list,
        queryRunner,
      );

      await queryRunner.commitTransaction();

      // await this.externalApiHelper.saveProductInViniculum(
      //   createdProduct,
      //   body.attributes_list,
      //   url_key_for_url_rewrites,
      // );

      // SYNC URL rewrites table WITH ELASTICSEARC
      // await this.syncUrlRewriteswithElasticSearch(
      //   url_rewrites_body.request_path,
      // );

      return createdProduct;
    } catch (err) {
      console.log(err);
      console.log('ERROR: Product creation failed', JSON.stringify(err));
      this.logger.error('Product Creation Failed', err);
      
      // Only attempt rollback if transaction is active
      try {
        if (queryRunner.isTransactionActive) {
          await queryRunner.rollbackTransaction();
        }
      } catch (rollbackErr) {
        // Log rollback error but don't throw it to preserve original error
        console.error('Error during transaction rollback:', rollbackErr);
      }
      
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException('Product Creation Failed');
      }
    } finally {
      await queryRunner.release();
    }
  }

  async updateMultipleProductsStatus(
    product_ids: number[],
    newStatus: boolean,
    headers: any,
  ) {
    try {
      const existingProducts = await this.catalogProductRepository.find({
        where: { id: In(product_ids) },
      });
      let existingProductsIds = existingProducts.map((e) => {
        return e.id;
      });

      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'bulk_update_status',
        user_meta_info: user_data,
      };

      let activityData = await this.createActivity(createActivityBody);

      let newStatusUpdateData: any = {
        status: newStatus,
        updated_by_action_details: {
          action_type: 'bulk_update_status',
          activity: activityData,
        },
      };

      // const data = await this.catalogProductRepository
      //   .createQueryBuilder()
      //   .update()
      //   .set({ status: newStatus })
      //   .where('id IN (:...ids)', { ids: existingProductsIds })
      //   .execute();

      for await (let product of existingProductsIds) {
        try {
          const data = await this.updateCatalogProduct(
            newStatusUpdateData,
            product,
            // headers,
          );
        } catch (error) {
          console.error(
            `Failed to update product with id ${product}:`,
            JSON.stringify(error),
          );
        }
      }

      return { response: 'Products status updated successfully' };
    } catch (err) {
      this.logger.error('Product Creation Failed', err);
      throw new InternalServerErrorException(
        'Failed to update products status',
      );
    }
  }

  async catalogProductFindById(product_ids: number[]) {
    try {
      const fetchProducts = await this.catalogProductRepository.find({
        where: {
          id: In(product_ids),
        },
      });

      return fetchProducts;
    } catch (error) {
      this.logger.error('Products Search Failed', error);
      throw new InternalServerErrorException(
        'Failed to retrieve product  list',
      );
    }
  }

  async listProducts(body: any) {
    try {
      let { filters, pagination } = body;

      const mainCatalogQuery: any = {
        relations: [
          'catalogProductFlatRelations',
          'productCategoryRelations.category',
          'inventoryAttributesRelations',
          'productImageRelation',
          'productVideoRelation',
        ],
        where: {},
      };

      let selectClause = {
        id: true,
        sku: true,
        status: true,
        type_id: true,
        created_at: true,
        updated_at: true,
        catalogProductFlatRelations: {
          id: true,
          name: true,
          price: true,
          special_price: true,
          weight: true,
          manufacturer: true,
          visibility: true,
          thumbnail: true,
          is_cod: true,
          hsn_code: true,
          tax_class_id: true,
          reward_point_product: true,
          pd_expiry_date: true,
          gtin: true,
          demo_available: true,
          description: true,
          short_description: true,
          packaging: true,
          warranty: true,
          key_specifications: true,
          features: true,
          msrp: true,
          url_key: true,
          international_active: true,
        },
        inventoryAttributesRelations: {
          id: true,
          is_in_stock: true,
          qty: true,
          min_sale_qty: true,
          max_sale_qty: true,
          backorders: true,
        },
        productCategoryRelations: {
          id: true,
          position: true,
          category: {
            id: true,
            name: true,
            status: true,
            parent_id: true,
          },
        },
        productImageRelation: {
          id: true,
          value: true,
          // position: true,
          // is_disabled: true,
          image_tags: true,
        },
        productVideoRelation: {
          id: true,
          // value: true,
          // url: true,
          // title: true,
          // description: true,
          // position: true,
        },
      };

      let labels = {};
      if (filters?.showCategoryNames) {
        labels['showCategoryNames'] = true;
      }

      if (filters?.showOptionsValues) {
        labels['showOptionsValues'] = true;
      }

      if (filters?.name || filters?.gstin || filters?.sku || filters?.url_key) {
        if (filters.name) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              name: filters.name,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          let transformedData =
            await this.mapperForListProductsAPI(catalogProductsArray);

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.sku) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              sku: filters.sku,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          let transformedData =
            await this.mapperForListProductsAPI(catalogProductsArray);

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.gstin) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              gtin: filters.gstin,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          let transformedData =
            await this.mapperForListProductsAPI(catalogProductsArray);

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.url_key) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              url_key: filters.url_key,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          let transformedData =
            await this.mapperForListProductsAPI(catalogProductsArray);

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }
      }

      if (filters?.id_from !== undefined && filters?.id_to !== undefined) {
        mainCatalogQuery.where = {
          id: Between(filters.id_from, filters.id_to),
        };
      } else if (filters.id_to !== undefined) {
        mainCatalogQuery.where = {
          id: LessThanOrEqual(filters.id_to),
        };
      } else if (filters.id_from !== undefined) {
        const maxId = await this.catalogProductRepository
          .createQueryBuilder()
          .select('MAX(id)', 'maxId')
          .getRawOne();

        const maxIdValue = maxId ? maxId.maxId : 100000;

        mainCatalogQuery.where = {
          id: Between(filters.id_from, maxIdValue),
        };
      }

      if (filters && filters.status) {
        mainCatalogQuery.where['status'] = filters.status;
      }

      // console.log("where",JSON.stringify(whereClause));

      if (filters && filters.type_id) {
        mainCatalogQuery.where['type_id'] = filters.type_id;
      }

      if (body.product_id && !filters?.id_from && !filters?.id_to) {
        mainCatalogQuery.where['id'] = body.product_id;
      }

      if (
        filters?.price_from ||
        filters?.price_to ||
        filters?.product_expiry_from ||
        filters?.product_expiry_to ||
        filters?.demo_available !== undefined ||
        filters?.visibility ||
        filters?.manufacturer ||
        filters?.msrp_from ||
        filters?.msrp_to ||
        body.search_by_keyword
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };

        const catalogProductFlatRelations: any = {};

        if (filters?.price_from || filters?.price_to) {
          if (
            filters.price_from !== undefined &&
            filters.price_to !== undefined
          ) {
            catalogProductFlatRelations.price = Between(
              filters.price_from,
              filters.price_to,
            );
          } else if (filters.price_to !== undefined) {
            catalogProductFlatRelations.price = LessThanOrEqual(
              filters.price_to,
            );
          } else if (filters.price_from !== undefined) {
            const maxPrice = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(price)', 'maxPrice')
              .getRawOne();

            const maxPriceValue = maxPrice ? maxPrice.maxPrice : null;

            catalogProductFlatRelations.price = Between(
              filters.price_from,
              maxPriceValue,
            );
          }
        }

        if (filters.product_expiry_from || filters.product_expiry_to) {
          if (
            filters.product_expiry_from !== undefined &&
            filters.product_expiry_to !== undefined
          ) {
            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry_from,
              filters.product_expiry_to,
            );
          } else if (filters.product_expiry_to !== undefined) {
            catalogProductFlatRelations.pd_expiry_date = LessThanOrEqual(
              filters.product_expiry_to,
            );
          } else if (filters.product_expiry_from !== undefined) {
            const maxProductExpiryDate = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(pd_expiry_date)', 'maxProductExpiryDate')
              .getRawOne();

            const maxProductExpiryValue = maxProductExpiryDate
              ? maxProductExpiryDate.maxProductExpiryDate
              : null;

            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry_from,
              maxProductExpiryValue,
            );
          }
        }

        if (filters?.demo_available !== undefined) {
          catalogProductFlatRelations.demo_available = filters.demo_available;
        }

        if (filters?.visibility !== undefined) {
          catalogProductFlatRelations.visibility = filters.visibility;
        }

        if (filters?.manufacturer !== undefined) {
          catalogProductFlatRelations.manufacturer = filters.manufacturer;
        }

        if (filters?.msrp_from || filters?.msrp_to) {
          if (
            filters.msrp_from !== undefined &&
            filters.msrp_to !== undefined
          ) {
            catalogProductFlatRelations.msrp = Between(
              filters.msrp_from,
              filters.msrp_to,
            );
          } else if (filters.msrp_to !== undefined) {
            catalogProductFlatRelations.msrp = LessThanOrEqual(filters.msrp_to);
          } else if (filters.msrp_from !== undefined) {
            const maxMsrp = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(msrp)', 'maxMsrp')
              .getRawOne();

            const maxMsrpValue = maxMsrp ? maxMsrp.maxMsrp : null;

            catalogProductFlatRelations.msrp = Between(
              filters.msrp_from,
              maxMsrpValue,
            );
          }
        }

        if (
          body.search_by_keyword &&
          (body.search_by_keyword.field == 'name' || 'sku') &&
          body.search_by_keyword.value
        ) {
          catalogProductFlatRelations[body.search_by_keyword.field] = Like(
            `%${body.search_by_keyword.value}%`,
          );
        }

        if (Object.keys(catalogProductFlatRelations)?.length > 0) {
          whereClause.catalogProductFlatRelations = {
            ...whereClause.catalogProductFlatRelations,
            ...catalogProductFlatRelations,
          };
        }


        mainCatalogQuery.where = whereClause;
      }

      if (
        filters?.backorders !== undefined ||
        filters?.is_in_stock !== undefined ||
        filters?.quantity
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };
        const inventoryAttributesRelations: any = {};

        if (filters?.backorders !== undefined) {
          inventoryAttributesRelations.backorders = filters.backorders;
        }

        if (filters?.is_in_stock !== undefined) {
          inventoryAttributesRelations.is_in_stock = filters.is_in_stock;
        }

        if (filters?.min_qty || filters.max_qty) {
          if (filters.min_qty !== undefined && filters.max_qty !== undefined) {
            inventoryAttributesRelations.qty = Between(
              filters.min_qty,
              filters.max_qty,
            );
          } else if (filters.max_qty !== undefined) {
            inventoryAttributesRelations.qty = LessThanOrEqual(filters.max_qty);
          } else if (filters.min_qty !== undefined) {
            const maxQty = await this.inventoryAttributesRepository
              .createQueryBuilder()
              .select('MAX(qty)', 'maxQty')
              .getRawOne();

            const maxQtyValue = maxQty ? maxQty.maxQty : null;

            inventoryAttributesRelations.qty = Between(
              filters.min_qty,
              maxQtyValue,
            );
          }
        }

        if (Object.keys(inventoryAttributesRelations)?.length > 0) {
          whereClause.inventoryAttributesRelations = {
            ...whereClause.inventoryAttributesRelations,
            ...inventoryAttributesRelations,
          };
        }

        mainCatalogQuery.where = whereClause;
      }

      
      mainCatalogQuery['skip'] =
        ((pagination?.page || 1) - 1) *
        (pagination?.size || env.sqlQueryResultsize);
      mainCatalogQuery['take'] = pagination?.size
        ? pagination?.size
        : env.sqlQueryResultsize;

      if (body.sort_by && body.sort_by.order && body.sort_by.field) {
        // console.log(sort_by.field);
        if (body.sort_by.field == 'id' || body.sort_by.field === 'type_id') {
          mainCatalogQuery.order = { id: body.sort_by.order };
        } else if (
          body.sort_by.field === 'qty' ||
          body.sort_by.field === 'is_in_stock' ||
          body.sort_by.field === 'backorders'
        ) {
          const order: any = {};

          order.inventoryAttributesRelations = {
            [body.sort_by.field]: body.sort_by.order,
          };
          mainCatalogQuery.order = order;
        } else {
          const sortCheck = await this.productAttributesNameRepository.findOne({
            where: { code: body.sort_by.field },
          });
          if (sortCheck && sortCheck.is_sortable == true) {
            const order: any = {};

            order.catalogProductFlatRelations = {
              [body.sort_by.field]: body.sort_by.order,
            };
            mainCatalogQuery.order = order;
          }
        }
      } else {
        mainCatalogQuery['order'] = {
          id: 'DESC',
        };
      }

      // const catalogProductValue = await this.catalogProductRepository.find({
      //   ...mainCatalogQuery,
      //   select: selectClause,
      // });
      let dataArray = [];

      dataArray.push(
        this.catalogProductRepository.find({
          ...mainCatalogQuery,
          select: selectClause,
        }),
      );

      let countPerPage = pagination?.page
        ? pagination?.size
        : env.sqlQueryResultsize;

      if (Object.keys(body.filters).length > 0) {
        const relationsToRemove = [
          'productCategoryRelations.category',
          // 'productImageRelation',
          'productVideoRelation',
        ];

        // Use filter to create a new array excluding the specified relations
        mainCatalogQuery.relations = mainCatalogQuery.relations.filter(
          (relation) => !relationsToRemove.includes(relation),
        );

        dataArray.push(this.catalogProductRepository.count(mainCatalogQuery));
      } else {
        dataArray.push(this.catalogProductRepository.count());
      }

      let listData = await Promise.all(dataArray);

      let modifiedData = await this.mapperForListProductsAPI(listData[0]);

      return {
        item_count: listData[1],
        pages_count: Math.ceil(listData[1] / countPerPage),
        page_no: pagination?.page,
        page_size: listData[0]?.length,
        items: modifiedData,
      };
    } catch (err) {
      this.logger.error('Failed to list Products', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException('Failed to list products');
      }
    }
  }

  async getProductsByIds(
    product_ids?: number[],
    filters?: any,
    search_by_keyword?: any,
    pagination?: any,
    sort_by?: any,
  ) {
    try {
      const mainCatalogQuery: any = {
        relations: [
          'inventoryAttributesRelations',
          'catalogProductFlatRelations',
          'tierPricesRelations',
          'productCategoryRelations.category',
          'productImageRelation',
          'productVideoRelation',
          'parentRelations.child',
        ],
      };

      let selectClause = {
        id: true,
        sku: true,
        status: true,
        type_id: true,
        created_at: true,
        updated_at: true,
        inventoryAttributesRelations: {
          id: true,
          is_in_stock: true,
          qty: true,
          min_sale_qty: true,
          max_sale_qty: true,
          backorders: true,
        },
        catalogProductFlatRelations: {
          id: true,
          name: true,
          description: true,
          short_description: true,
          price: true,
          special_price: true,
          weight: true,
          manufacturer: true,
          image: true,
          small_image: true,
          thumbnail: true,
          url_key: true,
          visibility: true,
          country_of_manufacture: true,
          msrp: true,
          tax_class_id: true,
          key_specifications: true,
          features: true,
          htext: true,
          packaging: true,
          hvideo: true,
          hsn_code: true,
          is_cod: true,
          warranty: true,
          reward_point_product: true,
          international_active: true,
          average_rating: true,
          rating_count: true,
          return_period: true,
          dispatch_days: true,
          demo_available: true,
          dentalkart_custom_fee: true,
          special_from_date: true,
          special_to_date: true,
          pd_expiry_date: true,
          gtin: true,
          news_from_date: true,
          news_to_date: true,
          swatch_image: true,
          meta_keyword: true,
          meta_title: true,
          meta_description: true,
          other_info: true,
        },
        // booleanAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        // stringAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        // integerAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        // decimalAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        // dateAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        // textAttributeValuesRelations: {
        //   id: true,
        //   value: true,
        //   attribute: {
        //     id: true,
        //     code: true,
        //   },
        // },
        tierPricesRelations: {
          id: true,
          value: true,
          quantity: true,
          customer_group: true,
          price_type: true,
        },
        productCategoryRelations: {
          id: true,
          position: true,
          category: {
            id: true,
            name: true,
            status: true,
            parent_id: true,
          },
        },
        parentRelations: {
          id: true,
          relation_type: true,
          position: true,
          child: {
            id: true,
            sku: true,
            status: true,
          },
        },
        productImageRelation: {
          id: true,
          value: true,
          position: true,
          is_disabled: true,
          image_tags: true,
        },
        productVideoRelation: {
          id: true,
          value: true,
          url: true,
          title: true,
          description: true,
          position: true,
        },
      };

      let labels = {};
      if (filters?.showCategoryNames) {
        labels['showCategoryNames'] = true;
        // showCategoryNames: true,
        // };
      }

      if (filters?.showOptionsValues) {
        labels['showOptionsValues'] = true;
      }
      if (filters?.name || filters?.gstin || filters?.sku || filters?.url_key) {
        if (filters.name) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              name: filters.name,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTable(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.sku) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              sku: filters.sku,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTable(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.gstin) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              gtin: filters.gstin,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTable(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.url_key) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              url_key: filters.url_key,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTable(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }
      }

      if (
        filters?.id &&
        filters.id?.from !== undefined &&
        filters.id?.to !== undefined &&
        product_ids == undefined
      ) {
        mainCatalogQuery.where = {
          id: Between(filters.id.from, filters.id.to),
        };
      } else if (
        filters?.id &&
        filters.id?.to !== undefined &&
        product_ids == undefined
      ) {
        mainCatalogQuery.where = {
          id: LessThanOrEqual(filters.id.to),
        };
      } else if (
        filters?.id &&
        filters.id?.from !== undefined &&
        product_ids == undefined
      ) {
        const maxId = await this.catalogProductRepository
          .createQueryBuilder()
          .select('MAX(id)', 'maxId')
          .getRawOne();

        const maxIdValue = maxId ? maxId.maxId : 100000;

        mainCatalogQuery.where = {
          id: Between(filters.id.from, maxIdValue),
        };
      }

      if (product_ids && product_ids?.length > 0 && !filters?.id) {
        mainCatalogQuery.where = { id: In(product_ids) };
      } else if (!filters?.id) {
        mainCatalogQuery.where = {};
        mainCatalogQuery.order = { id: 'DESC' };
      }

      if (filters && filters.status) {
        mainCatalogQuery.where['status'] = filters.status;
      }

      if (filters && filters.type_id) {
        mainCatalogQuery.where['type_id'] = filters.type_id;
      }

      if (
        filters?.price ||
        filters?.product_expiry ||
        filters?.demo_available !== undefined ||
        filters?.visibility ||
        filters?.manufacturer ||
        search_by_keyword
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };

        const catalogProductFlatRelations: any = {};

        if (filters?.price) {
          if (
            filters.price?.from !== undefined &&
            filters.price?.to !== undefined
          ) {
            catalogProductFlatRelations.price = Between(
              filters.price.from,
              filters.price.to,
            );
          } else if (filters.price?.to !== undefined) {
            catalogProductFlatRelations.price = LessThanOrEqual(
              filters.price.to,
            );
          } else if (filters.price?.from !== undefined) {
            const maxPrice = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(price)', 'maxPrice')
              .getRawOne();

            const maxPriceValue = maxPrice ? maxPrice.maxPrice : null;

            catalogProductFlatRelations.price = Between(
              filters.price.from,
              maxPriceValue,
            );
          }
        }

        if (filters?.product_expiry) {
          if (
            filters.product_expiry?.from !== undefined &&
            filters.product_expiry?.to !== undefined
          ) {
            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry.from,
              filters.product_expiry.to,
            );
          } else if (filters.product_expiry?.to !== undefined) {
            catalogProductFlatRelations.pd_expiry_date = LessThanOrEqual(
              filters.product_expiry.to,
            );
          } else if (filters.product_expiry?.from !== undefined) {
            const maxProductExpiryDate = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(pd_expiry_date)', 'maxProductExpiryDate')
              .getRawOne();

            const maxProductExpiryValue = maxProductExpiryDate
              ? maxProductExpiryDate.maxProductExpiryDate
              : null;

            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry.from,
              maxProductExpiryValue,
            );
          }
        }

        if (filters?.demo_available !== undefined) {
          catalogProductFlatRelations.demo_available = filters.demo_available;
        }

        if (filters?.visibility !== undefined) {
          catalogProductFlatRelations.visibility = filters.visibility;
        }

        if (filters?.manufacturer !== undefined) {
          catalogProductFlatRelations.manufacturer = filters.manufacturer;
        }

        if (
          search_by_keyword &&
          (search_by_keyword.field == 'name' || 'sku') &&
          search_by_keyword.value
        ) {
          catalogProductFlatRelations[search_by_keyword.field] = Like(
            `%${search_by_keyword.value}%`,
          );
        }

        if (Object.keys(catalogProductFlatRelations)?.length > 0) {
          whereClause.catalogProductFlatRelations = {
            ...whereClause.catalogProductFlatRelations,
            ...catalogProductFlatRelations,
          };
        }

        // console.log(whereClause);

        mainCatalogQuery.where = whereClause;
        // console.log('ok', mainCatalogQuery.where);
      }

      if (
        filters?.backorders !== undefined ||
        filters?.is_in_stock !== undefined ||
        filters?.quantity
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };

        const inventoryAttributesRelations: any = {};

        if (filters?.backorders !== undefined) {
          inventoryAttributesRelations.backorders = filters.backorders;
        }

        if (filters?.is_in_stock !== undefined) {
          inventoryAttributesRelations.is_in_stock = filters.is_in_stock;
        }

        if (filters?.quantity) {
          if (
            filters.quantity?.min !== undefined &&
            filters.quantity?.max !== undefined
          ) {
            inventoryAttributesRelations.qty = Between(
              filters.quantity.min,
              filters.quantity.max,
            );
          } else if (filters.quantity?.max !== undefined) {
            inventoryAttributesRelations.qty = LessThanOrEqual(
              filters.quantity.max,
            );
          } else if (filters.quantity?.min !== undefined) {
            const maxQty = await this.inventoryAttributesRepository
              .createQueryBuilder()
              .select('MAX(qty)', 'maxQty')
              .getRawOne();

            const maxQtyValue = maxQty ? maxQty.maxQty : null;

            inventoryAttributesRelations.qty = Between(
              filters.quantity.min,
              maxQtyValue,
            );
          }
        }

        if (Object.keys(inventoryAttributesRelations)?.length > 0) {
          whereClause.inventoryAttributesRelations = {
            ...whereClause.inventoryAttributesRelations,
            ...inventoryAttributesRelations,
          };
        }

        mainCatalogQuery.where = whereClause;
      }

      //PAGINATION LOGIC IS HERE
      mainCatalogQuery['skip'] =
        ((pagination?.page || 1) - 1) *
        (pagination?.size || env.sqlQueryResultsize);
      mainCatalogQuery['take'] = pagination?.size
        ? pagination?.size
        : env.sqlQueryResultsize;

      if (sort_by && sort_by.order && sort_by.field) {
        // console.log(sort_by.field);
        if (sort_by.field == 'id' || sort_by.field === 'type_id') {
          mainCatalogQuery.order = { id: sort_by.order };
        } else if (
          sort_by.field === 'qty' ||
          sort_by.field === 'is_in_stock' ||
          sort_by.field === 'backorders'
        ) {
          const order: any = {};

          order.inventoryAttributesRelations = {
            [sort_by.field]: sort_by.order,
          };
          mainCatalogQuery.order = order;
        } else {
          const sortCheck = await this.productAttributesNameRepository.findOne({
            where: { code: sort_by.field },
          });
          if (sortCheck && sortCheck.is_sortable == true) {
            const order: any = {};

            order.catalogProductFlatRelations = {
              [sort_by.field]: sort_by.order,
            };

            mainCatalogQuery.order = order;
          }
        }
      }

      const catalogProductValue = await this.catalogProductRepository.find({
        ...mainCatalogQuery,
        select: selectClause,
      });

      // const catalogProductValue =
      //   await this.catalogProductRepository.findAndCount({
      //     ...mainCatalogQuery,
      //     select: selectClause,
      //   });

      // let catalogProductsArray = catalogProductValue[0];
      // let catalogProductCount = catalogProductValue[1];

      // if (
      //   catalogProductsArray?.length === 1 &&
      //   catalogProductsArray[0].type_id === 'grouped'
      // ) {
      //   labels['showGroupedTierPrices'] = true;
      // }

      if (
        catalogProductValue?.length === 1 &&
        catalogProductValue[0].type_id === 'grouped'
      ) {
        labels['showGroupedTierPrices'] = true;
      }

      let transformedData = await this.mapperForProductsFromFlatTable(
        catalogProductValue,
        labels,
      );
      // console.log(transformedData);

      let dataCount;

      let countPerPage = pagination?.page
        ? pagination?.size
        : env.sqlQueryResultsize;

      const relationsToRemove = [
        'productCategoryRelations.category',
        'productImageRelation',
        'productVideoRelation',
        'tierPricesRelations',
        'parentRelations.child',
      ];

      // Use filter to create a new array excluding the specified relations
      mainCatalogQuery.relations = mainCatalogQuery.relations.filter(
        (relation) => !relationsToRemove.includes(relation),
      );

      dataCount = await this.catalogProductRepository.count(mainCatalogQuery);

      return {
        item_count: dataCount,
        pages_count: Math.ceil(dataCount / countPerPage),
        page_no: pagination?.page,
        page_size: transformedData?.length,
        items: transformedData,
      };
    } catch (error) {
      console.log(error);
      this.logger.error('Products Search Failed', error);
      throw new InternalServerErrorException(
        'Failed to retrieve products by IDs',
      );
    }
  }

  async getChildProductsByIds(id: number, page: number, size: number) {
    try {
      const childProducts = await this.catalogProductRelationsRepository.find({
        select: {
          id: true,
          position: true,
        },
        where: {
          parent: {
            id: id,
          },
        },
      });
      const childProductsData = await Promise.all(
        childProducts.map(async (product: any) => {
          const productData = await this.catalogProductFlatRepository.findOne({
            select: {
              id: true,
              sku: true,
              name: true,
              status: true,
              image: true,
              price: true,
              special_price: true,
              type_id: true,
              created_at: true,
              updated_at: true,
            },
            where: {
              id: product.child_id,
            },
          });
          
          if (productData) {
            const inventoryData = await this.inventoryAttributesRepository.findOne({
              select: {
                id: true,
                qty: true,
                is_in_stock: true,
                backorders: true,
              },
              where: {
                product: {
                  id: product.child_id,
                },
              },
            });
      
            return {
              ...productData,
              position: product.position,
              inventory: inventoryData || {
                qty: 0,
                is_in_stock: false,
                backorders: false
              }
            };
          }
          
          return null;
        })
      );
      
      const filteredData = childProductsData.filter(product => product !== null);
      const pageSize = size || 20;
      // Apply pagination
      const totalItems = filteredData.length;
      const totalPages = Math.ceil(totalItems / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedItems = filteredData.slice(startIndex, endIndex);
      
      return {
        items: paginatedItems,
        count: totalItems,
        page_count: totalPages,
        page: page,
        page_size: pageSize
      };
      

    } catch (error) {
      console.log(error);
      this.logger.error('Failed to get child products', error);
      throw new InternalServerErrorException('Failed to get child products');
    }
  }

  async updateGroupProductPosition(
    id: number,
    child_id: number,
    position: number,
    headers: {
      admin_identifier: string;
      platform?: string;
      user_agent?: string;
    },
  ) {
    
    // Create a query runner for transaction management
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // Start transaction
      
      // Check if parent exists
      const parentProduct = await queryRunner.manager.findOne(this.catalogProductRepository.target, {
        where: { id },
      });
      
      if (!parentProduct) {
        throw new BadRequestException('Parent product not found');
      }
      
      // Check if child relation exists
      const childProduct = await queryRunner.manager.findOne(this.catalogProductRelationsRepository.target, {
        where: { parent: { id }, child: { id: child_id } },
      });
      
      if (!childProduct) {
        throw new BadRequestException('Child product not found');
      }
      
      // Store previous position for activity log
      const previousPosition = childProduct.position;
      
      // Update position but don't save yet
      childProduct.position = position;
      
      // Prepare user metadata for activity log
      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent) user_meta_info['user_agent'] = headers.user_agent;
      
      const user_data = Object.keys(user_meta_info).length === 0 
        ? null 
        : JSON.stringify(user_meta_info);
      
      // Create activity entry
      const createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'update',
        user_meta_info: user_data,
      };
      
      // Save the activity
      const activityData = await this.createActivity(createActivityBody);
      
      // Important: Create separate objects for old and new values to capture the position change
      const oldValueData = { position: previousPosition };
      const newValueData = { position: position };
      
      // Save the child product with updated position
      await queryRunner.manager.save(childProduct);
      
      // Create activity log with the correct old and new values
      const createActivityLogsBody = {
        entity_id: child_id, // Using the child_id parameter, not childProduct.id
        old_value: JSON.stringify(oldValueData),
        new_value: JSON.stringify(newValueData),
        revert_log_id: null,
        activity: activityData
      };
      
      // Save the activity log
      const activityLogsData = await this.createActivityLog(createActivityLogsBody, queryRunner);
      
      // Create event outbox entry
      await this.createEventOutbox({
        entity_id: child_id,
        entity_type: EntityType.PRODUCT,
        activity_logs: activityLogsData,
      }, queryRunner);

      // Commit the transaction
      await queryRunner.commitTransaction();
      
      // Return the updated data of child product
      return childProduct;
      
    } catch (error) {
      // Rollback the transaction in case of error
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      
      this.logger.error('Failed to update group product position', error);
      
      if (error instanceof BadRequestException) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to update group product position');
      }
    } finally {
      // Always release the query runner
      await queryRunner.release();
    }
  }
  private async buildSelectClause(fields: string[]) {
    const allFields = [
      'name',
      'sku',
      'description',
      'type_id',
      'short_description',
      'weight',
      'status',
      'tax_class_id',
      'visibility',
      'price',
      'msrp',
      'special_price',
      'special_from_date',
      'special_to_date',
      'url_key',
      'meta_title',
      'meta_keyword',
      'meta_description',
      'manufacturer',
      'product_id',
      'dispatch_days',
      'packaging',
      'hsn_code',
      'pd_expiry_date',
      'international_active',
      'reward_point_product',
    ];

    let selectClause = {
      id: true,
      sku: true,
      status: true,
      type_id: true,
      created_at: true,
      updated_at: true,
      inventoryAttributesRelations: {
        id: true,
        is_in_stock: true,
        qty: true,
        min_sale_qty: true,
        max_sale_qty: true,
        backorders: true,
      },
      catalogProductFlatRelations: {
        id: true,
      },
    };
    for (const field of allFields) {
      if (fields?.includes(field)) {
        selectClause.catalogProductFlatRelations[field] = true;
      }
    }

    return selectClause;
  }

  async getSelectedProductDetailsByIds(
    product_ids?: number[],
    filters?: any,
    search_by_keyword?: any,
    pagination?: any,
    sort_by?: any,
    fields?: string[],
  ) {
    try {
      const mainCatalogQuery: any = {
        relations: [
          'inventoryAttributesRelations',
          'catalogProductFlatRelations',
          'productCategoryRelations.category',
        ],
      };

      const selectClause = await this.buildSelectClause(fields);

      let labels = {};
      if (filters?.showCategoryNames) {
        labels['showCategoryNames'] = true;
      }

      if (filters?.showOptionsValues) {
        labels['showOptionsValues'] = true;
      }
      if (filters?.name || filters?.gstin || filters?.sku || filters?.url_key) {
        if (filters.name) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              name: filters.name,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTableForCSV(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.sku) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              sku: filters.sku,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTableForCSV(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.gstin) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              gtin: filters.gstin,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) *
            (pagination?.size || env.sqlQueryResultsize);
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTableForCSV(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }

        if (filters.url_key) {
          mainCatalogQuery.where = {
            ...mainCatalogQuery.where,
            catalogProductFlatRelations: {
              url_key: filters.url_key,
            },
          };

          mainCatalogQuery['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          mainCatalogQuery['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;

          const catalogProductValue =
            await this.catalogProductRepository.findAndCount({
              ...mainCatalogQuery,
              select: selectClause,
            });

          let catalogProductsArray = catalogProductValue[0];
          let catalogProductCount = catalogProductValue[1];

          if (
            catalogProductsArray?.length === 1 &&
            catalogProductsArray[0].type_id === 'grouped'
          ) {
            labels['showGroupedTierPrices'] = true;
          }

          let transformedData = await this.mapperForProductsFromFlatTableForCSV(
            catalogProductsArray,
            labels,
          );

          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;

          return {
            item_count: catalogProductCount,
            pages_count: Math.ceil(catalogProductCount / countPerPage),
            page_no: pagination?.page,
            page_size: transformedData?.length,
            items: transformedData,
          };
        }
      }

      if (
        filters?.id &&
        filters.id?.from !== undefined &&
        filters.id?.to !== undefined &&
        product_ids == undefined
      ) {
        mainCatalogQuery.where = {
          id: Between(filters.id.from, filters.id.to),
        };
      } else if (
        filters?.id &&
        filters.id?.to !== undefined &&
        product_ids == undefined
      ) {
        mainCatalogQuery.where = {
          id: LessThanOrEqual(filters.id.to),
        };
      } else if (
        filters?.id &&
        filters.id?.from !== undefined &&
        product_ids == undefined
      ) {
        const maxId = await this.catalogProductRepository
          .createQueryBuilder()
          .select('MAX(id)', 'maxId')
          .getRawOne();

        const maxIdValue = maxId ? maxId.maxId : 100000;

        mainCatalogQuery.where = {
          id: Between(filters.id.from, maxIdValue),
        };
      }

      if (product_ids && product_ids?.length > 0 && !filters?.id) {
        mainCatalogQuery.where = { id: In(product_ids) };
      } else if (!filters?.id) {
        mainCatalogQuery.where = {};
        mainCatalogQuery.order = { id: 'DESC' };
      }

      if (filters && filters.status) {
        mainCatalogQuery.where['status'] = filters.status;
      }

      if (filters && filters.type_id) {
        mainCatalogQuery.where['type_id'] = filters.type_id;
      }

      if (
        filters?.price ||
        filters?.product_expiry ||
        filters?.demo_available !== undefined ||
        filters?.visibility ||
        filters?.manufacturer ||
        search_by_keyword
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };

        const catalogProductFlatRelations: any = {};

        if (filters?.price) {
          if (
            filters.price?.from !== undefined &&
            filters.price?.to !== undefined
          ) {
            catalogProductFlatRelations.price = Between(
              filters.price.from,
              filters.price.to,
            );
          } else if (filters.price?.to !== undefined) {
            catalogProductFlatRelations.price = LessThanOrEqual(
              filters.price.to,
            );
          } else if (filters.price?.from !== undefined) {
            const maxPrice = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(price)', 'maxPrice')
              .getRawOne();

            const maxPriceValue = maxPrice ? maxPrice.maxPrice : null;

            catalogProductFlatRelations.price = Between(
              filters.price.from,
              maxPriceValue,
            );
          }
        }

        if (filters?.product_expiry) {
          if (
            filters.product_expiry?.from !== undefined &&
            filters.product_expiry?.to !== undefined
          ) {
            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry.from,
              filters.product_expiry.to,
            );
          } else if (filters.product_expiry?.to !== undefined) {
            catalogProductFlatRelations.pd_expiry_date = LessThanOrEqual(
              filters.product_expiry.to,
            );
          } else if (filters.product_expiry?.from !== undefined) {
            const maxProductExpiryDate = await this.catalogProductFlatRepository
              .createQueryBuilder()
              .select('MAX(pd_expiry_date)', 'maxProductExpiryDate')
              .getRawOne();

            const maxProductExpiryValue = maxProductExpiryDate
              ? maxProductExpiryDate.maxProductExpiryDate
              : null;

            catalogProductFlatRelations.pd_expiry_date = Between(
              filters.product_expiry.from,
              maxProductExpiryValue,
            );
          }
        }

        if (filters?.demo_available !== undefined) {
          catalogProductFlatRelations.demo_available = filters.demo_available;
        }

        if (filters?.visibility !== undefined) {
          catalogProductFlatRelations.visibility = filters.visibility;
        }

        if (filters?.manufacturer !== undefined) {
          catalogProductFlatRelations.manufacturer = filters.manufacturer;
        }

        if (
          search_by_keyword &&
          (search_by_keyword.field == 'name' || 'sku') &&
          search_by_keyword.value
        ) {
          catalogProductFlatRelations[search_by_keyword.field] = Like(
            `%${search_by_keyword.value}%`,
          );
        }

        if (Object.keys(catalogProductFlatRelations)?.length > 0) {
          whereClause.catalogProductFlatRelations = {
            ...whereClause.catalogProductFlatRelations,
            ...catalogProductFlatRelations,
          };
        }

        mainCatalogQuery.where = whereClause;
      }

      if (
        filters?.backorders !== undefined ||
        filters?.is_in_stock !== undefined ||
        filters?.quantity
      ) {
        const whereClause: any = { ...mainCatalogQuery.where };

        const inventoryAttributesRelations: any = {};

        if (filters?.backorders !== undefined) {
          inventoryAttributesRelations.backorders = filters.backorders;
        }

        if (filters?.is_in_stock !== undefined) {
          inventoryAttributesRelations.is_in_stock = filters.is_in_stock;
        }

        if (filters?.quantity) {
          if (
            filters.quantity?.min !== undefined &&
            filters.quantity?.max !== undefined
          ) {
            inventoryAttributesRelations.qty = Between(
              filters.quantity.min,
              filters.quantity.max,
            );
          } else if (filters.quantity?.max !== undefined) {
            inventoryAttributesRelations.qty = LessThanOrEqual(
              filters.quantity.max,
            );
          } else if (filters.quantity?.min !== undefined) {
            const maxQty = await this.inventoryAttributesRepository
              .createQueryBuilder()
              .select('MAX(qty)', 'maxQty')
              .getRawOne();

            const maxQtyValue = maxQty ? maxQty.maxQty : null;

            inventoryAttributesRelations.qty = Between(
              filters.quantity.min,
              maxQtyValue,
            );
          }
        }

        if (Object.keys(inventoryAttributesRelations)?.length > 0) {
          whereClause.inventoryAttributesRelations = {
            ...whereClause.inventoryAttributesRelations,
            ...inventoryAttributesRelations,
          };
        }

        mainCatalogQuery.where = whereClause;
      }

      //PAGINATION LOGIC IS HERE
      mainCatalogQuery['skip'] =
        ((pagination?.page || 1) - 1) *
        (pagination?.size || env.sqlQueryResultsize);
      mainCatalogQuery['take'] = pagination?.size
        ? pagination?.size
        : env.sqlQueryResultsize;

      if (sort_by && sort_by.order && sort_by.field) {
        // console.log(sort_by.field);
        if (sort_by.field == 'id' || sort_by.field === 'type_id') {
          mainCatalogQuery.order = { id: sort_by.order };
        } else if (
          sort_by.field === 'qty' ||
          sort_by.field === 'is_in_stock' ||
          sort_by.field === 'backorders'
        ) {
          const order: any = {};

          order.inventoryAttributesRelations = {
            [sort_by.field]: sort_by.order,
          };
          mainCatalogQuery.order = order;
        } else {
          const sortCheck = await this.productAttributesNameRepository.findOne({
            where: { code: sort_by.field },
          });
          if (sortCheck && sortCheck.is_sortable == true) {
            const order: any = {};

            order.catalogProductFlatRelations = {
              [sort_by.field]: sort_by.order,
            };

            mainCatalogQuery.order = order;
          }
        }
      }

      const catalogProductValue = await this.catalogProductRepository.find({
        ...mainCatalogQuery,
        select: selectClause,
      });

      if (
        catalogProductValue?.length === 1 &&
        catalogProductValue[0].type_id === 'grouped'
      ) {
        labels['showGroupedTierPrices'] = true;
      }

      let transformedData = await this.mapperForProductsFromFlatTableForCSV(
        catalogProductValue,
        labels,
      );

      let dataCount;

      let countPerPage = pagination?.page
        ? pagination?.size
        : env.sqlQueryResultsize;

      const relationsToRemove = [
        'productCategoryRelations.category',
        'productImageRelation',
        'productVideoRelation',
        'tierPricesRelations',
        'parentRelations.child',
      ];

      // Use filter to create a new array excluding the specified relations
      mainCatalogQuery.relations = mainCatalogQuery.relations.filter(
        (relation) => !relationsToRemove.includes(relation),
      );

      dataCount = await this.catalogProductRepository.count(mainCatalogQuery);

      return {
        item_count: dataCount,
        pages_count: Math.ceil(dataCount / countPerPage),
        page_no: pagination?.page,
        page_size: transformedData?.length,
        items: transformedData,
      };
    } catch (error) {
      console.log(error);
      this.logger.error('Products Search Failed', error);
      throw new InternalServerErrorException(
        'Failed to retrieve products by IDs',
      );
    }
  }

  async updateCatalogProduct(
    body: UpdateCatalogProductDto,
    id: number,
    headers?: any,
  ) {
    console.log("body",body)
    // console.log("id",id);
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const existingProduct = await this.catalogProductFindOneMethod({
        where: { id },
      });

      //CREATES DEEP COPY OF THE OBJECT
      let {
        inventory_details,
        category_associated,
        attributes_list,
        tier_prices,
        product_links,
      } = body;

      const newValue = JSON.parse(JSON.stringify(body));

      if (!existingProduct)
        throw new BadRequestException('Unable to edit this product');

      // Update status if provided in the request body
      if ('status' in body) {
        existingProduct.status = body.status;
      }

      let productName = body?.attributes_list?.find(
        (attribute) => attribute.attribute_code === 'name',
      )?.value;

      if (productName === null || productName?.trim() === '') {
        throw new BadRequestException('Product name cannot be empty');
      }

      let mrpValue = await body.attributes_list?.find(
        (attribute) => attribute.attribute_code === 'price',
      )?.value;

      let sellingPriceValue = await body.attributes_list?.find(
        (attribute) => attribute.attribute_code === 'special_price',
      )?.value;

      if (Number(sellingPriceValue) > Number(mrpValue)) {
        throw new BadRequestException(
          'Selling price cannot be greater than MRP',
        );
      }

      if (attributes_list) {
        const flatAttributes: any = body.attributes_list.reduce(
          (acc, { attribute_code, value }) => {
            acc[attribute_code] = value;
            return acc;
          },
          {},
        );

        flatAttributes.pd_expiry_date = new Date(flatAttributes.pd_expiry_date);

        let attributeOptionsValueArray = [];
        flatAttributes.visibility &&
          attributeOptionsValueArray.push('visibility');

        flatAttributes.manufacturer &&
          attributeOptionsValueArray.push('manufacturer');

        flatAttributes.tax_class_id &&
          attributeOptionsValueArray.push('tax_class_id');

        flatAttributes.dispatch_days &&
          attributeOptionsValueArray.push('dispatch_days');

        if (attributeOptionsValueArray.length > 0) {
          const existingOptionsForAttributesWithOptions =
            await this.productAttributeOptionsRepository.find({
              where: { attribute: { code: In(attributeOptionsValueArray) } },
              relations: ['attribute'],
              select: {
                id: true,
                value: true,
                attribute: {
                  id: true,
                  code: true,
                },
              },
            });

          const attributeOptionMap = new Map();

          existingOptionsForAttributesWithOptions.forEach((option) => {
            const attributeCode = option.attribute.code;
            if (!attributeOptionMap.has(attributeCode)) {
              attributeOptionMap.set(attributeCode, new Map());
            }
            attributeOptionMap.get(attributeCode).set(option.id, option);
          });

          if (
            flatAttributes.visibility &&
            attributeOptionMap
              .get('visibility')
              ?.get(Number(flatAttributes.visibility)) == undefined
          ) {
            throw new BadRequestException(
              'Invalid value provided for visibility',
            );
          }

          if (
            flatAttributes.manufacturer &&
            attributeOptionMap
              .get('manufacturer')
              ?.get(Number(flatAttributes.manufacturer)) == undefined
          ) {
            throw new BadRequestException(
              'Invalid value provided for manufacturer',
            );
          }

          if (
            flatAttributes.dispatch_days &&
            attributeOptionMap
              .get('dispatch_days')
              ?.get(Number(flatAttributes.dispatch_days)) == undefined
          ) {
            throw new BadRequestException(
              'Invalid value provided for dispatch_days',
            );
          }

          if (
            flatAttributes.tax_class_id &&
            attributeOptionMap
              .get('tax_class_id')
              ?.get(Number(flatAttributes.tax_class_id)) == undefined
          ) {
            throw new BadRequestException(
              'Invalid value provided for tax_class_id',
            );
          }
        }
      }


      const fetchCompleteProduct = await this.getProductsByIds(
        [id],
        null,
        null,
        null,
      );

      // console.log("fetchCompleteProduct",fetchCompleteProduct)
      let createActivityBody;

      //DELETED group_tier_price key if it is present in grouped product to handle old and new changes
      delete fetchCompleteProduct.items[0]['group_tier_price'];
      delete fetchCompleteProduct.items[0]['updated_by_action_details'];
      delete newValue['group_tier_price'];
      delete newValue['updated_by_action_details'];

      let createActivityLogsBody = {
        entity_id: existingProduct.id,
        old_value: fetchCompleteProduct.items[0],
        new_value: newValue,
        revert_log_id: body?.updated_by_action_details?.revert_log_id || null,
      };

      let activityData, activityLogsData;

      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);

      // Save updated product entity
      const updatedProduct =
        await this.catalogProductRepository.save(existingProduct);

      if (body?.updated_by_action_details?.activity == undefined) {
        createActivityBody = {
          user: headers.admin_identifier,
          entity: EntityTypeEnum.PRODUCT,
          activity_type: 'update',
          user_meta_info: user_data,
        };

        activityData = await this.createActivity(createActivityBody);
      } else {
        activityData = body.updated_by_action_details.activity;
      }

      let promisesArray = [];

      if (inventory_details)
        promisesArray.push(
          this.saveInventoryData(
            inventory_details,
            updatedProduct,
            queryRunner,
          ),
        );

      if (product_links) {
        promisesArray.push(
          this.saveProductLinksData(product_links, updatedProduct, queryRunner),
        );
      }

      if (tier_prices && updatedProduct.type_id !== 'grouped')
        promisesArray.push(
          this.saveTierPrices(tier_prices, updatedProduct, queryRunner),
        );

      let urlKeyAttribute, url_rewrites_body;

      if (attributes_list) {
        urlKeyAttribute = attributes_list.find(
          (attribute) => attribute.attribute_code === 'url_key',
        );
        if (urlKeyAttribute?.value) {
          urlKeyAttribute.value = urlKeyAttribute.value
            .trim()
            .replace(/\s+/g, '-');

          const existing_url_key =
            await this.stringAttributeValuesRepository.findOne({
              where: {
                value: urlKeyAttribute.value,
                attribute: { code: 'url_key' },
              },
              relations: ['attribute'],
            });

          const existing_url_key_in_rewrites =
            await this.urlRewritesRepository.findOne({
              where: { request_path: `${urlKeyAttribute.value}.html` },
            });
          if (existing_url_key || existing_url_key_in_rewrites) {
            throw new BadRequestException('Provided url_key already exists');
          } else {
            url_rewrites_body = {
              entity_id: updatedProduct.id,
              entity_type: EntityTypeEnum.PRODUCT,
              request_path: `${urlKeyAttribute.value}.html`,
              target_path: String(updatedProduct.id),
              old_request_path:
                urlKeyAttribute.enableRedirect === true
                  ? `${fetchCompleteProduct.items[0].attributes_list.url_key}.html`
                  : undefined,
            };

            // promisesArray.push(
            //   this.modifyUrlRewrites(url_rewrites_body, queryRunner),
            // );

            const { oldUrlRewrite, updatedUrlRewrite, createdUrlRewrite } =
              await this.modifyUrlRewritesV2(url_rewrites_body, queryRunner);

            if (oldUrlRewrite) {
              await Promise.all([
                this.eventsLogService.saveActivityAndEvent({
                  headers,
                  entityId: oldUrlRewrite.id,
                  entityType: EntityType.URL_REWRITE,
                  activityEntityType: EntityTypeEnum.URL_REWRITE,
                  activityType: 'modify-product-url-rewrite',
                  queryRunner: queryRunner,
                  updatedValue: updatedUrlRewrite,
                  previousValue: oldUrlRewrite,
                }),
                this.eventsLogService.saveActivityAndEvent({
                  headers,
                  entityId: createdUrlRewrite.id,
                  entityType: EntityType.URL_REWRITE,
                  activityEntityType: EntityTypeEnum.URL_REWRITE,
                  activityType: 'create-product-url-rewrite',
                  queryRunner: queryRunner,
                  updatedValue: null,
                  previousValue: null,
                }),
              ]);
            } else {
              await this.eventsLogService.saveActivityAndEvent({
                headers,
                entityId: createdUrlRewrite.id,
                activityEntityType: EntityTypeEnum.URL_REWRITE,
                entityType: EntityType.URL_REWRITE,
                activityType: 'create-product-url-rewrite',
                queryRunner: queryRunner,
                updatedValue: null,
                previousValue: null,
              });
            }
          }
        }

        promisesArray.push(
          this.saveProductAttributesInRespectiveTables(
            attributes_list,
            updatedProduct,
            { saveAction: SavingAction.UPDATE },
            queryRunner,
          ),
        );
      }

      if (category_associated) {
        promisesArray.push(
          this.saveProductCategoryRelation(
            category_associated,
            updatedProduct,
            queryRunner,
          ),
        );
      }

      console.log("promisesArray======",JSON.stringify(promisesArray))

      let resolvedpromises = await Promise.all(promisesArray);

      await this.saveProductInFlatTable(
        existingProduct,
        body.attributes_list,
        queryRunner,
      ),
        (activityLogsData = await this.createActivityLog(
          {
            ...createActivityLogsBody,
            activity: activityData,
          },
          queryRunner,
        )),
        await this.createEventOutbox(
          {
            entity_id: existingProduct.id,
            entity_type: EntityType.PRODUCT,
            activity_logs: activityLogsData,
          },
          queryRunner,
        );

      await queryRunner.commitTransaction();

      if (inventory_details?.is_in_stock === true) {
        await this.notifyCustomerAboutStockAvailability(existingProduct.id);
      }

      // console.log(body.attributes_list, 'BAL');

      const fetchUpdatedProduct = await this.getProductsByIds(
        [id],
        null,
        null,
        null,
      );

      // await this.externalApiHelper.updateProductAndSkuInViniculum(
      //   fetchUpdatedProduct.items,
      // );

      // if (
      //   urlKeyAttribute?.value &&
      //   url_rewrites_body?.old_request_path !== undefined
      // ) {
      //   await Promise.all([
      //     this.syncUrlRewriteswithElasticSearch(url_rewrites_body.request_path),
      //     this.syncUrlRewriteswithElasticSearch(
      //       url_rewrites_body.old_request_path,
      //     ),
      //   ]);
      // } else if (
      //   urlKeyAttribute?.value &&
      //   url_rewrites_body?.old_request_path === undefined
      // ) {
      //   await this.syncUrlRewriteswithElasticSearch(
      //     url_rewrites_body.request_path,
      //   );
      // }

      return fetchUpdatedProduct.items[0];
    } catch (error) {
      console.log(error);
      console.log('Product updatation failed', JSON.stringify(error));
      this.logger.error('Product updatation failed', error);
      await queryRunner.rollbackTransaction();
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to update catalog product',
        );
      }
    } finally {
      await queryRunner.release();
    }
  }

  async getAttributesTypewise() {
    try {
      const productAttributes = await this.productAttributesFind();

      const attributesByProductType = {
        simple: [],
        virtual: [],
        grouped: [],
      };

      productAttributes.forEach((attribute) => {
        if (!attribute.apply_to) {
          attributesByProductType.simple.push(attribute);
          attributesByProductType.virtual.push(attribute);
          attributesByProductType.grouped.push(attribute);
        } else {
          const productTypes = attribute.apply_to.split(',');
          productTypes.forEach((productType) => {
            attributesByProductType[productType.trim()].push(attribute);
          });
        }
      });

      return attributesByProductType;
    } catch (error) {
      this.logger.error('Attributes List Search Failed', error);
      throw new InternalServerErrorException(
        'Failed to retrieve product attributes list',
      );
    }
  }

  async createAttributesGroup(body: CreateAttributesGroupDto) {
    try {
      const { id, group_name } = body;
      const existingAttributeGroup = await this.attributesGroupFindOneByMethod({
        id,
        group_name,
      });

      if (existingAttributeGroup)
        throw new BadRequestException(
          'Attribute Group with this name already exists!',
        );

      const createdAttributeGroup = this.attributesGroupRepository.create(body);

      await this.attributesGroupRepository.save(createdAttributeGroup);

      return createdAttributeGroup;
    } catch (error) {
      this.logger.error('Attribute Group Creation Failed', error);
      throw new InternalServerErrorException(
        'Failed to create attribute group',
      );
    }
  }

  async getAttributesGroupList() {
    try {
      const productAttributesGroup = await this.attributesGroupFindMethod();

      return productAttributesGroup;
    } catch (error) {
      this.logger.error('Attributes Group Search Failed', error);
      throw new InternalServerErrorException(
        'Failed to retrieve product attributes group list',
      );
    }
  }

  async updateAttributesGroup(body: UpdateAttributesGroupDto) {
    const existingAttributesGroup = await this.attributesGroupFindOneMethod({
      where: { id: body.id },
    });

    // console.log(existingAttributesGroup, 'existingAttributesGroup');

    if (!existingAttributesGroup)
      throw new BadRequestException('unable to edit this attribute group');

    const updatedAttributesGroup = await this.attributesGroupRepository.save({
      ...existingAttributesGroup,
      ...body,
    });

    return updatedAttributesGroup;
  }

  async fetchProductById(productId: number) {
    try {
      return await this.catalogProductRepository.findOne({
        where: { id: productId },
      });
    } catch (error) {
      throw new Error(`Error fetching product: ${error.message}`);
    }
  }

  async addDataInAttributesGroupRelation(
    body: CreateAttributesGroupRelationDto,
  ) {
    try {
      const { attribute_id, group_id, sort_order } = body;
      const attributeRelation =
        await this.attributesGroupRelationsFindOneMethod({
          where: { attribute: { id: attribute_id } },
        });

      if (attributeRelation) {
        throw new BadRequestException(
          'This attribute is already present in another group',
        );
      }

      const existingProductAttribute = await this.productAttributesFindOne({
        where: { id: attribute_id },
      });
      // console.log(existingProductAttribute);
      const existingAttributeGroup = await this.attributesGroupFindOneMethod({
        where: { id: group_id },
      });
      // console.log(existingAttributeGroup);
      if (!existingProductAttribute || !existingAttributeGroup) {
        throw new NotFoundException(
          'This attribute id or group id is incorrect',
        );
      }

      const createdAttributeGroupRelation =
        this.attributesGroupRelationRepository.create({
          attribute: existingProductAttribute,
          group: existingAttributeGroup,
          sort_order,
        });

      await this.attributesGroupRelationRepository.save(
        createdAttributeGroupRelation,
      );

      return createdAttributeGroupRelation;
    } catch (err) {
      this.logger.error('Adding Data Failed', err);
      throw new BadRequestException('Failed to add data');
    }
  }

  async getAttributeRelationsData(groupId?: number) {
    try {
      let relationsQuery = this.attributesGroupRelationRepository
        .createQueryBuilder('relation')
        .leftJoinAndSelect('relation.attribute', 'attribute')
        .leftJoinAndSelect('relation.group', 'group');

      if (groupId) {
        relationsQuery = relationsQuery.where('relation.group_id = :groupId', {
          groupId,
        });
      }
      const relations = await relationsQuery.getMany();
      const groupedRelations = {};
      relations.forEach((relation) => {
        //   console.log(relation);
        const { group, attribute, sort_order } = relation;
        if (group && relation) {
          if (!groupedRelations[group.id]) {
            groupedRelations[group.id] = {
              group: {
                id: group.id,
                group_name: group.group_name,
                group_code: group.group_code,
                sort_order: group.sort_order,
                created_at: group.created_at,
              },
              attributes: [],
            };
          }
          groupedRelations[group.id].attributes.push({
            ...attribute,
            sort_order,
          });
        }
      });

      Object.keys(groupedRelations).forEach((groupId) => {
        groupedRelations[groupId].attributes.sort((a, b) => {
          if (a.sort_order === b.sort_order) {
            // If sort_order is the same, compare by object id
            return a.id - b.id;
          }
          return a.sort_order - b.sort_order;
        });
      });

      return groupedRelations;
    } catch (err) {
      this.logger.error('Attribute Relations Data Search Failed', err);
      throw new InternalServerErrorException('Data Search Failed');
    }
  }

  async updateAttributeRelationsData(
    attributeId: number,
    newSortOrder: number,
  ) {
    try {
      const relation = await this.attributesGroupRelationsFindOneMethod({
        where: {
          attribute: { id: attributeId },
        },
      });
      // console.log(relation);
      if (!relation) {
        throw new BadRequestException('Attribute group relation not found');
      }

      relation.sort_order = newSortOrder;

      await this.attributesGroupRelationRepository.save(relation);
      return relation;
    } catch (err) {
      this.logger.error('Failed to update sort order', err);
      throw new InternalServerErrorException('Failed to update sort order');
    }
  }

  async deleteAttributeGroupRelation(attributeId: number) {
    const relation = await this.attributesGroupRelationsFindOneMethod({
      where: {
        attribute: { id: attributeId },
      },
    });
    // console.log(relation);
    if (!relation) {
      throw new BadRequestException('Attribute group relation not found');
    }

    await this.attributesGroupRelationRepository.remove(relation);
  }

  async createAttributeOptions(
    attribute_id: number,
    attribute_values: string[],
    queryRunner?: QueryRunner,
  ) {
    try {
      const existingProductAttribute = await this.productAttributesFindOne({
        where: { id: attribute_id, frontend_input: 'select' },
      });

      if (!existingProductAttribute) {
        throw new BadRequestException(
          'This attribute cannot have options value',
        );
      }

      const newAttributeOptions = [];

      // Iterate over the array of attribute values
      for (const attribute_value of attribute_values) {
        const checkForExistingOptions =
          await this.productAttributesOptionsFindOneMethod({
            where: {
              attribute: { id: existingProductAttribute.id },
              value: attribute_value,
            },
          });

        if (!checkForExistingOptions) {
          const attributeOption = this.productAttributeOptionsRepository.create(
            {
              attribute: existingProductAttribute,
              value: attribute_value,
            },
          );

          // Save the attribute option to the database
          const savedAttributeOption = queryRunner
            ? await queryRunner.manager.save(
                ProductAttributesOptions,
                attributeOption,
              )
            : await this.productAttributeOptionsRepository.save(
                attributeOption,
              );

          // Add the saved attribute option to the array
          newAttributeOptions.push(savedAttributeOption);
        } else {
          continue;
        }
      }

      // Return the array of saved attribute options
      return { new_options_list: newAttributeOptions };
    } catch (err) {
      this.logger.error('Attribute options creation failed', err);
      throw new InternalServerErrorException(
        'Attribute option creation failed',
      );
    }
  }

  async handleMedia(
    files: Express.Multer.File[],
    productId: number,
    mediaData: any,
    headers: any,
  ) {
    try {
      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'product_media',
        // user_meta_info: user_data,
      };

      let fetchMediaData = await this.getProductsByIds(
        [productId],
        null,
        null,
        null,
      );
      let existingMediaData = fetchMediaData.items[0]?.media_gallery_entries;

      let mediaDataLength = existingMediaData.length;

      let activityData = await this.createActivity(createActivityBody);

      const parsedMediaData = JSON.parse(mediaData.mediaData);
      const maxImageSizeMB = parseInt(process.env.MAX_IMAGE_SIZE_MB);
      const maxVideoSizeMB = parseInt(process.env.MAX_VIDEO_SIZE_MB);

      const allowedTags = ['swatch', 'base', 'thumbnail', 'small'];

      // let deletedItems = [];

      for (const key in parsedMediaData) {
        const item = parsedMediaData[key];
        if (item.delete === true && item.id && !item.isVideo) {
          let key = item.value.split('/').pop();
          await this.s3Service.deleteFiles([key]);
          const fetchMediaGallaryData =
            await this.mediaGallaryRepository.findOne({
              where: { id: item.id, product: { id: productId } },
            });

          // key.endsWith('.mp4')
          //   ? await this.mediaGallaryVideoRepository.delete({
          //       product: { id: productId },
          //       id: item.id,
          //     })
          // :
          await this.mediaGallaryRepository.delete({
            product: { id: productId },
            id: item.id,
          });

          // deletedItems.push(fetchMediaGallaryData);

          if (
            fetchMediaGallaryData &&
            fetchMediaGallaryData.image_tags !== null
          ) {
            let tagValues = fetchMediaGallaryData?.image_tags
              .split(',')
              ?.map((item) => item.trim());

            let attributes_list_array = [];

            tagValues.forEach(async (value) => {
              const attributeData =
                await this.productAttributesNameRepository.findOne({
                  where: { label: value },
                });
              attributes_list_array.push({
                attribute_code: attributeData?.code,
                value: null,
              });
            });

            let attributes_list_data: any = {
              attributes_list: attributes_list_array,
            };

            await this.updateCatalogProduct(
              attributes_list_data,
              productId,
              headers,
            );
          }
        } else if (item.id && !item.delete && !item.isVideo) {
          const isValidTags = item.image_tags?.every((tag) =>
            allowedTags.includes(tag),
          );

          if (
            !isValidTags ||
            item.image_tags == null
            // item.is_disabled == undefined ||
            // item.position == undefined
          ) {
            continue;
          }

          const mediaGallaryData = await this.mediaGallaryRepository.find({
            where: { product: { id: productId } },
          });
          const imageTagsArray = mediaGallaryData
            ?.filter((e) => e.id !== item.id)
            ?.map((e) => e?.image_tags?.split(', '))
            .flat();
          // .map((tag) => tag.trim());

          let flat_image_tags = item?.image_tags;

          const isTagPresent = flat_image_tags?.some((tag) =>
            imageTagsArray.includes(tag.trim().toLowerCase()),
          );

          if (!isTagPresent) {
            const existingImageData = await this.mediaGallaryRepository.findOne(
              { where: { product: { id: productId }, id: item.id } },
            );

            let oldImageTags;
            existingImageData?.image_tags !== null
              ? (oldImageTags = existingImageData?.image_tags)
              : // .split(', ')
                (oldImageTags = null);

            let image_tags_value =
              item.image_tags !== null ? item.image_tags?.join(', ') : null;
            await this.mediaGallaryRepository.update(
              { product: { id: productId }, id: item.id },
              {
                image_tags: image_tags_value,
                is_disabled: item?.is_disabled,
                position: item?.position,
              },
            );

            if (oldImageTags !== null) {
              let oldTagsList = oldImageTags
                ?.split(',')
                .map((item) => item.trim());

              let attributes_list_array = [];

              oldTagsList?.forEach(async (value) => {
                const attributeData =
                  await this.productAttributesNameRepository.findOne({
                    where: { label: value },
                  });

                attributes_list_array.push({
                  attribute_code: attributeData?.code,
                  value: null,
                });
              });

              let attributes_list_data: any = {
                attributes_list: attributes_list_array,
              };

              await this.updateCatalogProduct(
                attributes_list_data,
                productId,
                headers,
              );
            }

            if (item.image_tags !== null) {
              let attributes_list_array = [];

              item.image_tags?.forEach(async (value) => {
                const attributeData =
                  await this.productAttributesNameRepository.findOne({
                    where: { label: value },
                  });

                attributes_list_array.push({
                  attribute_code: attributeData?.code,
                  value: existingImageData.value,
                });
              });

              let attributes_list_data: any = {
                attributes_list: attributes_list_array,
              };

              await this.updateCatalogProduct(
                attributes_list_data,
                productId,
                headers,
              );
            }
          } else {
            // Get the tags of the current image
            const currentImageTags = item.image_tags || [];
            const tagsToProcess = currentImageTags.map((tag) =>
              tag.trim().toLowerCase(),
            );

            // Iterate over all media items to check for existing tags
            for (const mediaItem of mediaGallaryData) {
              if (mediaItem.id !== item.id) {
                // Skip the current image
                // Split the tags of the other image into an array
                let otherImageTags = mediaItem.image_tags
                  ? mediaItem.image_tags.split(',').map((tag) => tag.trim())
                  : [];

                // Check for each tag in the current image
                tagsToProcess.forEach(async (tag) => {
                  const tagIndex = otherImageTags.findIndex(
                    (existingTag) => existingTag.toLowerCase() === tag,
                  );
                  if (tagIndex !== -1) {
                    // If the tag exists in the other image, remove it
                    otherImageTags.splice(tagIndex, 1);

                    // Update the other image in the database
                    await this.mediaGallaryRepository.update(
                      { id: mediaItem.id },
                      { image_tags: otherImageTags.join(', ') },
                    );
                  }
                });
              }
            }

            // Now update the current image with the new tags
            const image_tags_value = item.image_tags
              ? item.image_tags.join(', ')
              : null;

            await this.mediaGallaryRepository.update(
              { product: { id: productId }, id: item.id },
              {
                image_tags: image_tags_value,
                is_disabled: item?.is_disabled,
                position: item?.position,
              },
            );

            // Optionally, handle the attributes as before
            let attributes_list_array = [];

            for (const value of item.image_tags) {
              const attributeData =
                await this.productAttributesNameRepository.findOne({
                  where: { label: value },
                });

              attributes_list_array.push({
                attribute_code: attributeData?.code,
                value: item.value, // Use the appropriate value if needed
              });
            }

            let attributes_list_data: any = {
              attributes_list: attributes_list_array,
            };

            await this.updateCatalogProduct(
              attributes_list_data,
              productId,
              headers,
            );
          }
        } else if (item.id && !item.delete && item.isVideo == true) {
          const existingDetails =
            await this.mediaGallaryVideoRepository.findOne({
              where: { id: item.id, product: { id: productId } },
            });

          if (!existingDetails) {
            continue;
          }

          const updateFields = Object.assign(
            {},
            item.title !== undefined && { title: item.title },
            item.description !== undefined && { description: item.description },
            item.is_disabled !== undefined && { is_disabled: item.is_disabled },
            item.position !== undefined && { position: item.position },
          );

          // Update the entity if there are fields to update
          if (Object.keys(updateFields)?.length > 0) {
            await this.mediaGallaryVideoRepository.update(
              { id: existingDetails.id },
              updateFields,
            );
          }
        } else if (item.id && item.delete && item.isVideo == true) {
          // const existingDetails =
          //   await this.mediaGallaryVideoRepository.findOne({
          //     where: { id: item.id, product: { id: productId } },
          //   });

          // if (!existingDetails) {
          //   continue;
          // }

          await this.mediaGallaryVideoRepository.delete({
            product: { id: productId },
            id: item.id,
          });
        }
      }

      const fetchProduct = await this.fetchProductById(productId);

      const uploadedMedia = [];
      let incompleteImages = [];

      if (files && files.length > 0) {
        let existingBaseTags = await this.mediaGallaryRepository.findOne({
          where: {
            product: { id: productId },
          },
        });

        for (let i = 0; i < files.length; i++) {
          const file = files[i];

          // const fileName = file.originalname.split('.')[0];
          const fileName = file.originalname.split('.').slice(0, -1).join('.');
          let additionalDataForFile = parsedMediaData[fileName];

          additionalDataForFile['position'] = Number(`${mediaDataLength + 1}`);
          mediaDataLength++;

          const isValidTags = additionalDataForFile?.image_tags?.every((tag) =>
            allowedTags.includes(tag),
          );

          if (!file || !additionalDataForFile || !isValidTags) {
            incompleteImages.push(file.originalname);
            continue;
          }

          // if (!isValidTags && additionalDataForFile.image_tags !== null) {
          //   continue;
          // }

          // Check file size
          const fileSizeMB = this.getFileSizeMB(file);
          if (
            (file.mimetype.startsWith('image') &&
              fileSizeMB > maxImageSizeMB) ||
            (file.mimetype.startsWith('video') && fileSizeMB > maxVideoSizeMB)
          ) {
            throw new BadRequestException(
              `File size exceeds the maximum limit (${fileSizeMB}MB > ${
                file.mimetype.startsWith('image')
                  ? maxImageSizeMB
                  : maxVideoSizeMB
              }MB)`,
            );
          }

          if (!existingBaseTags && i === 0) {
            additionalDataForFile.image_tags = ['base', 'thumbnail'];
          }

          const mediaEntity = await this.saveMediaToDatabase(
            file,
            additionalDataForFile,
            fetchProduct,
            headers,
          );
          uploadedMedia.push(mediaEntity);
        }
      }

      if (files.length === 1 && incompleteImages.length === 1) {
        throw new BadRequestException(
          'Failed to upload this image due to incomplete data',
        );
      }

      let fetchMediaDataAfterChanges = await this.getProductsByIds(
        [productId],
        null,
        null,
        null,
      );
      let currentMediaData =
        fetchMediaDataAfterChanges.items[0]?.media_gallery_entries;

      let createActivityLogsBody = {
        entity_id: productId,
        old_value: { media_gallery_entries: existingMediaData },
        new_value: { media_gallery_entries: currentMediaData },
      };

      let activityLogsData = await this.createActivityLog({
        ...createActivityLogsBody,
        activity: activityData,
      });

      await this.createEventOutbox({
        entity_id: productId,
        entity_type: EntityType.PRODUCT,
        activity_logs: activityLogsData,
      });

      return {
        message: 'Product Media Files managed successfully',
        data: { uploadedMedia },
        failedImages: incompleteImages,
      };
    } catch (error) {
      console.log('Media changes failed', JSON.stringify(error));
      this.logger.error('Failed to upload media for products', error);
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to update media files');
      }
    }
  }

  getFileSizeMB(file: Express.Multer.File): number {
    return Math.round(file.size / (1024 * 1024)); // Convert bytes to megabytes
  }

  private async buildMediaImageName(
    name: string,
    // isVideo: boolean,
  ): Promise<string> {
    let nameParts = name.split('.');
    let fileName = nameParts[0];
    let extension = nameParts[1];
    let imageName = `s3/${env.aws.productImagesFolderName}/${fileName}.${extension}`;
    let count = 1;

    while (
      await this.isMediaImageNameExists(
        imageName,
        // isVideo
      )
    ) {
      imageName = `s3/${env.aws.productImagesFolderName}/${fileName}_${count}.${extension}`;
      count++;
    }

    return imageName.split('/').pop();
  }

  private async isMediaImageNameExists(
    name: string,
    // isVideo: boolean,
  ): Promise<boolean> {
    // const repository = isVideo
    //   ? this.mediaGallaryVideoRepository
    //   : this.mediaGallaryRepository;
    const existingProduct = await this.mediaGallaryRepository.findOne({
      where: { value: name },
    });
    return !!existingProduct;
  }

  private async saveMediaToDatabase(
    file: Express.Multer.File,
    additionalDataForFile: any,
    product: any,
    headers: any,
  ): Promise<MediaGallary | MediaGallaryVideo> {
    try {
      let mediaEntity;

      if (file.mimetype.startsWith('image')) {
        let fileData = file;
        let fetchedProduct = product;
        // let isVideo = false;

        const usableImageName = await this.buildMediaImageName(
          fileData.originalname,
          // isVideo,
        );
        fileData.originalname = usableImageName;
        let fileUrl = await this.s3Service.uploadFilesToS3(
          [fileData],
          `${env.aws.productImagesFolderName}`,
        );

        let modifiedFileUrlKey = `s3/${
          env.aws.productImagesFolderName
        }/${fileUrl[0].split('/').pop()}`;

        const mediaGallaryData = await this.mediaGallaryRepository.find({
          where: { product: { id: fetchedProduct.id } },
        });

        const imageTagsArray = mediaGallaryData
          ?.map((e) => e?.image_tags?.split(', '))
          .flat();
        // .map((tag) => tag.trim());

        let flat_image_tags = additionalDataForFile?.image_tags;

        let isTagPresent;

        additionalDataForFile.image_tags == null
          ? (isTagPresent = true)
          : (isTagPresent = flat_image_tags.some((tag) =>
              imageTagsArray.includes(tag.trim().toLowerCase()),
            ));

        mediaEntity = new MediaGallary();
        !isTagPresent
          ? (mediaEntity.image_tags =
              additionalDataForFile.image_tags.join(', '))
          : (mediaEntity.image_tags = null);
        mediaEntity.value = modifiedFileUrlKey;
        mediaEntity.product = product;
        mediaEntity.position = additionalDataForFile.position;
        mediaEntity.is_disabled = additionalDataForFile.isDisabled;

        if (mediaEntity.image_tags !== null) {
          let attributesList = additionalDataForFile?.image_tags.map((value) =>
            value.trim(),
          );

          let attributes_list_array: { attribute_code: string; value: any }[] =
            [];

          attributesList.forEach(async (value) => {
            const attributeData =
              await this.productAttributesNameRepository.findOne({
                where: { label: value },
              });

            attributes_list_array.push({
              attribute_code: attributeData?.code,
              value: mediaEntity.value,
            });
          });

          let attributes_list_data: any = {
            attributes_list: attributes_list_array,
          };

          await this.updateCatalogProduct(
            attributes_list_data,
            fetchedProduct.id,
            headers,
          );
        }

        return await this.mediaGallaryRepository.save(mediaEntity);
      }
      //  else if (file.mimetype.startsWith('video')) {
      //   mediaEntity = new MediaGallaryVideo();
      //   let fileData = file;
      //   let isVideo: true;
      //   let fetchedProduct = product;

      //   const productData = await this.catalogProductRepository.findOne({
      //     where: { id: fetchedProduct.id },
      //   });

      //   const usableImageName = await this.buildMediaImageName(
      //     fileData.originalname,
      //     isVideo,
      //   );
      //   fileData.originalname = usableImageName;
      //   const fileUrl = await this.s3Service.uploadFilesToS3([fileData]);
      //   mediaEntity.product = productData;
      //   mediaEntity.value = fileUrl;
      //   mediaEntity.url = additionalDataForFile?.url;
      //   mediaEntity.title = additionalDataForFile?.title;
      //   mediaEntity.description = additionalDataForFile?.description;
      //   mediaEntity.is_disabled = additionalDataForFile?.is_disabled;

      //   return await this.mediaGallaryVideoRepository.save(mediaEntity);
      // }
      else {
        throw new Error('Unsupported file type');
      }
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to save media for products in database', error);
      throw new InternalServerErrorException('Failed to save media in databse');
    }
  }

  async updateAttributeOptions(options_id: number, attribute_value: string) {
    try {
      const existingProductAttributeOption =
        await this.productAttributesOptionsFindOneMethod({
          where: { id: options_id },
        });

      if (!existingProductAttributeOption) {
        throw new BadRequestException('Invalid attribute Options');
      }

      existingProductAttributeOption.value = attribute_value;

      await this.productAttributeOptionsRepository.save(
        existingProductAttributeOption,
      );
      return existingProductAttributeOption;
    } catch (error) {
      this.logger.error('Attributes option update failed', error);
      throw new BadRequestException('Attributes option update failed');
    }
  }

  async deleteAttributeOptions(optionsId: number) {
    try {
      const existingProductAttributeOptions =
        await this.productAttributesOptionsFindOneMethod({
          relations: ['attribute'],
          where: { id: optionsId },
        });
      // console.log(existingProductAttributeOptions.attribute.id);
      if (!existingProductAttributeOptions) {
        throw new BadRequestException('Invalid attribute options id');
      }

      const productsAssociatedWithThisOptionId =
        await this.intAttributeValuesFindMethod({
          relations: ['attribute'],
          where: {
            value: optionsId,
            attribute: { id: existingProductAttributeOptions.attribute.id },
          },
        });

      // console.log(productsAssociatedWithThisOptionId);

      if (productsAssociatedWithThisOptionId.length == 0) {
        throw new BadRequestException(
          'Unable to delete this attribute option id, please delete all the attribute values associated with this option id first',
        );
      } else {
        await this.productAttributeOptionsRepository.remove(
          existingProductAttributeOptions,
        );
        return existingProductAttributeOptions;
      }
    } catch (error) {
      console.log(error);
      this.logger.error('Attribute option deletion failed', error);
      throw new InternalServerErrorException(
        'Attribute option deletion failed',
      );
    }
  }

  async getAttributeOptionsList(
    attributeId?: number,
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ) {
    try {
      let optionsQuery =
        this.productAttributeOptionsRepository.createQueryBuilder('options');

      if (attributeId) {
        optionsQuery = optionsQuery
          .leftJoinAndSelect('options.attribute', 'attribute')
          .where('attribute.id = :attributeId', { attributeId });
      } else {
        optionsQuery = optionsQuery.leftJoinAndSelect(
          'options.attribute',
          'attribute',
        );
      }
      optionsQuery = optionsQuery.orderBy('options.created_at', sortOrder);
      const options = await optionsQuery.getMany();
      return options;
    } catch (error) {
      this.logger.error('Failed to fetch parent category', error);
      throw new InternalServerErrorException('Failed to fetch parent category');
    }
  }

  private async saveProductAttributesInRespectiveTables(
    attributes_list,
    createdProduct,
    saveActionObject,
    queryRunner,
  ) {
    try {
      let { saveAction } = saveActionObject;

      console.time('attributes-TIMER');

      const intAttributes = [];
      const stringAttributes = [];
      const decimalAttributes = [];
      const textAttributes = [];
      const dateAttributes = [];
      const booleanAttributes = [];
      const intWithOptionsAttributes = [];

      //FETCH THE LIST OF ATTRIBUTES FROM DB
      // console.time('ATTRIBUTESFETCH');
      const attributeListFromDb = await this.productAttributesNameRepository
        .createQueryBuilder()
        .getMany();
      // const attributeListFromDb = await this.productAttributesFind();
      // console.timeEnd('ATTRIBUTESFETCH');

      // const requiredAttributes = attributeListFromDb.filter(
      //   (attribute) => attribute.is_required,
      // );
      // console.log(requiredAttributes);

      //LOOP TO CHECK IF ALL THE REQUIRED ATTRIBUTES ARE PRESENT OR NOT
      // for (const requiredAttribute of requiredAttributes) {
      //   const isAttributePresent = attributes_list.some(
      //     (attribute) => attribute.attribute_code === requiredAttribute.code,
      //   );
      //   if (!isAttributePresent) {
      //     throw new BadRequestException(
      //       `Some of the mandatory attributes are missing`,
      //     );
      //   }
      // }
      // console.log(attributes_list, 'AL');
      const attributeCodeMap = {};
      for await (const attribute of attributeListFromDb) {
        attributeCodeMap[attribute.code] = {
          backendType: attribute.backend_type,
          frontendInput: attribute.frontend_input,
          attributeData: attribute,
        };
      }
      //LOOP TO CHECK WHICH ATTRIBUTE WILL BE PUSHED TO WHICH TABLE
      for await (const attributeObject of attributes_list) {
        const { attribute_code } = attributeObject;
        if (attributeCodeMap.hasOwnProperty(attribute_code)) {
          const { backendType, frontendInput, attributeData } =
            attributeCodeMap[attribute_code];
          if (
            backendType === AttributesValidationTypes.INT &&
            frontendInput !== AttributesValidationTypes.SELECT
          ) {
            intAttributes.push({
              ...attributeObject,
              value:
                typeof attributeObject.value === 'boolean'? attributeObject.value ? 1 : 0 :attributeObject.value,
              attributeData,
            });
          } else if (backendType === AttributesValidationTypes.VARCHAR) {
            stringAttributes.push({ ...attributeObject, attributeData });
          } else if (backendType === AttributesValidationTypes.DECIMAL) {
            decimalAttributes.push({ ...attributeObject, attributeData });
          } else if (backendType === AttributesValidationTypes.DATETIME) {
            dateAttributes.push({ ...attributeObject, attributeData });
          } else if (backendType === AttributesValidationTypes.TEXT) {
            textAttributes.push({ ...attributeObject, attributeData });
          } else if (backendType === AttributesValidationTypes.BOOLEAN) {
            booleanAttributes.push({ ...attributeObject, attributeData });
          } else if (
            backendType === AttributesValidationTypes.INT &&
            frontendInput === AttributesValidationTypes.SELECT
          ) {
            intWithOptionsAttributes.push({
              ...attributeObject,
              attributeData,
            });
          }
        }
      }

      /*

  METHOD TO SAVE ATTRIBUTES IN VARCHAR TABLE

  */

      const attributePromises = [];

      if (textAttributes.length !== 0) {
        const textAttributePromise = this.processTextAttributes(
          queryRunner,
          createdProduct,
          textAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(textAttributePromise);
      }

      if (booleanAttributes.length !== 0) {
        const booleanAttributePromise = this.processBooleanAttributes(
          queryRunner,
          createdProduct,
          booleanAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(booleanAttributePromise);
      }

      if (decimalAttributes.length !== 0) {
        const decimalAttributePromise = this.processDecimalAttributes(
          queryRunner,
          createdProduct,
          decimalAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(decimalAttributePromise);
      }

      if (stringAttributes.length !== 0) {
        const stringAttributePromise = this.processStringAttributes(
          queryRunner,
          createdProduct,
          stringAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(stringAttributePromise);
      }

      if (dateAttributes.length !== 0) {
        const dateAttributePromise = this.processDateAttributes(
          queryRunner,
          createdProduct,
          dateAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(dateAttributePromise);
      }

      if (intAttributes.length !== 0) {
        const intAttributePromise = this.processIntAttributes(
          queryRunner,
          createdProduct,
          intAttributes,
          attributeListFromDb,
          saveAction,
        );
        attributePromises.push(intAttributePromise);
      }

      if (intWithOptionsAttributes.length !== 0) {
        const intWithOptionsAttributesPromise =
          this.processIntWithOptionsAttributes(
            queryRunner,
            createdProduct,
            intWithOptionsAttributes,
          );
        attributePromises.push(intWithOptionsAttributesPromise);
      }
      await Promise.all(attributePromises);
      console.timeEnd('attributes-TIMER');
    } catch (err) {
      console.log(err, 'Error');
      this.logger.error('Failed to save product attributes', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to save product attributes',
        );
      }
    }
  }

  private async saveTierPrices(tier_prices, createdProduct, queryRunner) {
    try {
      console.time('tierprice-TIMER');

      // Find existing tier prices for the created product
      const findProductsTierPrice = await queryRunner.manager.find(TierPrices, {
        relations: ['product'],
        where: { product: { id: createdProduct.id } },
      });

      // Creating a Map for quicker lookup of existing tier prices
      const tierPriceMap = new Map();
      for (const tierPrice of findProductsTierPrice) {
        tierPriceMap.set(
          `${tierPrice.quantity}-${tierPrice.value}-${tierPrice.customer_group}-${tierPrice.price_type}`,
          tierPrice,
        );
      }
      // Create arrays for tier prices to insert, update, and delete
      const tierPricesToInsert = [];
      const tierPricesToUpdate = [];
      const tierPricesToDelete = [];

      // Create a Set to track which existing tier prices are to be retained
      const retainedTierPrices = new Set();

      const uniqueNewTierPricesMap = new Map();

      for (const newTierPrice of tier_prices) {
        const key = `${newTierPrice.qty}-${newTierPrice.value}-${newTierPrice.customer_group}-${newTierPrice.price_type}`;

        // Store unique tier prices in a Map
        if (!uniqueNewTierPricesMap.has(key)) {
          uniqueNewTierPricesMap.set(key, newTierPrice);
        }
      }

      // Iterate through the unique tier prices and compare with existing ones
      for (const [key, newTierPrice] of uniqueNewTierPricesMap.entries()) {
        const existingTierPrice = tierPriceMap.get(key);

        if (existingTierPrice) {
          // If the tier price exists, add it to the update array
          tierPricesToUpdate.push(existingTierPrice);
          // Mark this existing tier price as retained
          retainedTierPrices.add(key);
        } else {
          // If the tier price doesn't exist, add it to the insert array
          const tierPrice = new TierPrices();
          tierPrice.quantity = newTierPrice.qty;
          tierPrice.value = newTierPrice.value;
          tierPrice.product = createdProduct;
          tierPrice.customer_group = newTierPrice.customer_group;
          tierPrice.price_type = newTierPrice.price_type;
          tierPricesToInsert.push(tierPrice);
        }
      }

      // Find tier prices to delete (existing tier prices not retained)
      for (const [key, existingTierPrice] of tierPriceMap.entries()) {
        if (!retainedTierPrices.has(key)) {
          tierPricesToDelete.push(existingTierPrice);
        }
      }

      try {
        if (findProductsTierPrice.length > 0) {
          await Promise.all[
            (queryRunner.manager.remove(TierPrices, tierPricesToDelete),
            queryRunner.manager.save(TierPrices, tierPricesToInsert),
            queryRunner.manager.save(TierPrices, tierPricesToUpdate))
          ];
        } else {
          await queryRunner.manager.save(TierPrices, tierPricesToInsert);
        }
        console.timeEnd('tierprice-TIMER');
      } catch (error) {
        throw error;
      }
    } catch (err) {
      this.logger.error('Failed to save tier prices', err);
      throw new BadRequestException('Failed to save tier prices');
    }
  }

  private async saveInventoryData(inventory_data, createdProduct, queryRunner) {
    try {
      console.time('INVENTORY-TIMER');
      const { is_in_stock, backorders, min_sale_qty, max_sale_qty, qty } =
        inventory_data;
      const existingInventoryDetails =
        await this.inventoryAttributesFindOneMethod({
          relations: ['product'],
          where: {
            product: { id: createdProduct.id },
          },
        });

      if (!existingInventoryDetails) {
        await queryRunner.manager.save(InventoryAttributes, {
          ...inventory_data,
          product: createdProduct,
        });
      } else {
        await queryRunner.manager.update(
          InventoryAttributes,
          { product: createdProduct },
          { is_in_stock, backorders, min_sale_qty, max_sale_qty, qty },
        );
      }

      const updatedInventoryDetails = await queryRunner.manager.findOne(
        InventoryAttributes,
        {
          relations: ['product'],
          where: { product: { id: createdProduct.id } },
        },
      );

      // Determine the correct value for is_in_stock
      const adjustedIsInStock =
        backorders ||
        updatedInventoryDetails.qty > updatedInventoryDetails.min_sale_qty;

      if (updatedInventoryDetails.is_in_stock !== adjustedIsInStock) {
        await queryRunner.manager.update(
          InventoryAttributes,
          { product: createdProduct },
          { is_in_stock: adjustedIsInStock },
        );
      }

      console.timeEnd('INVENTORY-TIMER');
    } catch (err) {
      this.logger.error('Failed to update inventory details', err);
      throw new InternalServerErrorException(
        'Failed to update inventory details',
      );
    }
  }

  private async saveProductLinksData(
    product_links: {
      associated?: { product_id: number; position: number }[];
      upsell?: { product_id: number; position: number }[];
      crosssell?: { product_id: number; position: number }[];
      related?: { product_id: number; position: number }[];
    },
    createdProduct,
    queryRunner,
  ) {
    try {
      console.time('PRODUCT LINKS');
      // Fetch existing product relations
      const existingProductRelations =
        await this.catalogProductRelationsFindMethod({
          relations: ['child'],
          where: { parent: { id: createdProduct.id } },
          // transaction: queryRunner,
        });

      // Extract child product IDs from the new product links
      const childProductsId = Object.values(product_links)
        .flat()
        .map((link: any) => link.product_id);

      // Fetch child product data
      const childProductData = await this.catalogProductFindMethod({
        where: {
          id: In(childProductsId.filter((id) => id !== createdProduct.id)),
        },
      });
      // Create maps for efficient lookups
      const existingRelationsMap = new Map<string, CatalogProductRelation>();
      const newRelationsMap = new Map<string, CatalogProductRelation>();

      existingProductRelations.forEach((relation) => {
        const key = `${relation.relation_type}-${relation.child.id}-${relation.position}`;
        existingRelationsMap.set(key, relation);
      });

      Object.entries(product_links).forEach(([relationType, products]) => {
        products.forEach((product) => {
          const child = childProductData.find(
            (child) => child.id === product.product_id,
          );
          const key = `${relationType}-${product.product_id}-${product.position}`;
          if (child) {
            const relation = new CatalogProductRelation();
            relation.relation_type = relationType;
            relation.parent = createdProduct;
            relation.child = child;
            relation.position = product.position;
            newRelationsMap.set(key, relation);
          }
        });
      });

      // Identify relations to add or update
      const relationsToAddOrUpdate = [];
      newRelationsMap.forEach((newRelation, key) => {
        if (!existingRelationsMap.has(key)) {
          relationsToAddOrUpdate.push(newRelation);
        }
      });

      // Identify relations to remove
      const relationsToRemove = [];
      existingRelationsMap.forEach((existingRelation, key) => {
        if (!newRelationsMap.has(key)) {
          relationsToRemove.push(existingRelation);
        }
      });

      console.log('relationsToAddOrUpdate', { relationsToRemove, relationsToAddOrUpdate });

      // Perform database operations
      await Promise.all([
        queryRunner.manager.remove(relationsToRemove),
        queryRunner.manager.save(relationsToAddOrUpdate),
      ]);
      console.timeEnd('PRODUCT LINKS');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to save product links', err);
      throw new InternalServerErrorException('Failed to save product links');
    }
  }

  private async saveProductCategoryRelation(
    category_associated: number[],
    createdProduct,
    queryRunner,
  ) {
    try {
      console.time('procatrel-TIMER');

      // Fetch existing product category relations
      const existingRelations = await this.productCategoryRelationFindMethod({
        where: { product: { id: createdProduct.id } },
        relations: ['category'],
      });
      // console.log(existingRelations, 'EXISTING');

      // Extract existing category IDs
      const existingCategoryIds = existingRelations?.map(
        (relation) => relation.category.id,
      );

      // Convert arrays to sets for faster lookups
      const existingCategoryIdsSet = new Set(existingCategoryIds);
      const categoryAssociatedSet = new Set(category_associated);

      // Find categories to add (new categories not in existing relations)
      const categoriesToAdd = category_associated.filter(
        (categoryId) => !existingCategoryIdsSet.has(categoryId),
      );

      // Find relations to remove (existing relations not in new categories)
      const relationsToRemove = existingRelations.filter(
        (relation) => !categoryAssociatedSet.has(relation.category.id),
      );
      // Fetch existing category entities
      const existingCategories = await this.catalogCategoryRepository.find({
        where: { id: In(category_associated) },
      });

      const existingCategoryMap = new Map();
      for (const category of existingCategories) {
        existingCategoryMap.set(category.id, category);
      }

      // Create new relations for categories to add
      const categoriesToAddDataPromises = categoriesToAdd.map(
        async (categoryId) => {
          // Fetch the last position
          const fetchLastPosition = await queryRunner.manager.count(
            ProductCategoryRelation,
            {
              where: { category: { id: categoryId } },
            },
          );

          // Create a new ProductCategoryRelation instance
          const productCategoryRelation = new ProductCategoryRelation();
          productCategoryRelation.category =
            existingCategoryMap.get(categoryId);
          productCategoryRelation.product = createdProduct;
          productCategoryRelation.position = fetchLastPosition + 1;

          return productCategoryRelation;
        },
      );

      const categoriesToAddData = await Promise.all(
        categoriesToAddDataPromises,
      );

      // await queryRunner.manager.transaction(async (manager) => {
      // Remove relations for categories to remove
      await queryRunner.manager.remove(relationsToRemove);

      // Save new relations for categories to add
      await queryRunner.manager.save(categoriesToAddData);
      // });
      console.timeEnd('procatrel-TIMER');
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to add products in their respective categories',
        err,
      );
      throw new BadRequestException(
        'Failed to add products in their respective categories',
      );
    }
  }

  private async saveProductInFlatTable(
    createdProduct,
    attributes_list,
    queryRunner,
  ) {
    try {
      console.time('flat table');
      const existingRelation = await this.catalogProductFlatRepository.findOne({
        where: { product: { id: createdProduct.id } },
        relations: ['product'],
      });

      let url_key_data = await queryRunner.manager.findOne(
        StringAttributeValues,
        {
          relations: ['attribute', 'product'],
          where: {
            attribute: { code: 'url_key' },
            product: { id: createdProduct.id },
          },
        },
      );

      let data = {
        sku: createdProduct.sku,
        status: createdProduct.status,
        type_id: createdProduct.type_id,
      };

      const attributeObject = attributes_list?.reduce(
        (result, { attribute_code, value }) => {
          result[attribute_code] = value;
          return result;
        },
        {},
      );

      const flatData = { ...data, ...attributeObject };
      // console.log(flatData);

      if (existingRelation) {
        await queryRunner.manager.update(
          CatalogProductFlat,
          {
            sku: existingRelation.sku,
          },
          {
            status: flatData?.status,
            type_id: flatData?.type_id,
            name: flatData?.name,
            description: flatData?.description,
            short_description: flatData?.short_description,
            price: flatData?.price,
            special_price: flatData?.special_price || flatData?.price,
            weight: flatData?.weight,
            manufacturer: flatData?.manufacturer,
            image: flatData?.image,
            small_image: flatData?.small_image,
            swatch_image: flatData?.swatch_image,
            thumbnail: flatData?.thumbnail,
            news_from_date: flatData?.news_from_date,
            news_to_date: flatData?.news_to_date,
            url_key: url_key_data?.value,
            visibility: flatData?.visibility,
            country_of_manufacture: flatData?.country_of_manufacture,
            msrp: flatData?.msrp,
            tax_class_id: flatData?.tax_class_id,
            key_specifications: flatData?.key_specifications,
            features: flatData?.features,
            htext: flatData?.htext,
            packaging: flatData?.packaging,
            hvideo: flatData?.hvideo,
            hsn_code: flatData?.hsn_code,
            is_cod: flatData?.is_cod,
            warranty: flatData?.warranty,
            product_faq: flatData?.product_faq,
            reward_point_product: flatData?.reward_point_product,
            international_active: flatData?.international_active,
            average_rating: flatData?.average_rating,
            rating_count: flatData?.rating_count,
            return_period: flatData?.return_period,
            dispatch_days: flatData?.dispatch_days,
            dentalkart_custom_fee: flatData?.dentalkart_custom_fee,
            demo_available: flatData?.demo_available,
            special_from_date: flatData?.special_from_date,
            special_to_date: flatData?.special_to_date,
            pd_expiry_date: flatData?.pd_expiry_date,
            gtin: flatData.gtin,
            meta_title: flatData?.meta_title,
            meta_keyword: flatData?.meta_keyword,
            meta_description: flatData?.meta_description,
            other_info: flatData?.other_info,
          },
        );
      } else {
        const catalogProductInFlatTable =
          this.catalogProductFlatRepository.create({
            sku: flatData.sku,
            status: flatData.status,
            type_id: flatData.type_id,
            name: flatData.name,
            description: flatData.description,
            short_description: flatData.short_description,
            price: flatData.price,
            special_price: flatData.special_price || flatData.price,
            weight: flatData.weight,
            manufacturer: flatData.manufacturer,
            image: flatData.image,
            small_image: flatData.small_image,
            swatch_image: flatData?.swatch_image,
            thumbnail: flatData.thumbnail,
            news_from_date: flatData.news_from_date,
            news_to_date: flatData.news_to_date,
            url_key: url_key_data?.value,
            visibility: flatData.visibility,
            country_of_manufacture: flatData.country_of_manufacture,
            msrp: flatData.msrp,
            tax_class_id: flatData.tax_class_id,
            key_specifications: flatData.key_specifications,
            features: flatData.features,
            htext: flatData.htext,
            packaging: flatData.packaging,
            hvideo: flatData.hvideo,
            hsn_code: flatData.hsn_code,
            is_cod: flatData.is_cod,
            warranty: flatData.warranty,
            product_faq: flatData.product_faq,
            reward_point_product: flatData.reward_point_product,
            international_active: flatData.international_active,
            average_rating: flatData.average_rating,
            rating_count: flatData.rating_count,
            return_period: flatData.return_period,
            dispatch_days: flatData.dispatch_days,
            dentalkart_custom_fee: flatData.dentalkart_custom_fee,
            demo_available: flatData.demo_available,
            product: createdProduct,
            special_from_date: flatData.special_from_date,
            special_to_date: flatData.special_to_date,
            pd_expiry_date: flatData?.pd_expiry_date,
            gtin: flatData.gtin,
            meta_title: flatData?.meta_title,
            meta_keyword: flatData?.meta_keyword,
            meta_description: flatData?.meta_description,
            other_info: flatData?.other_info,
          });

        await queryRunner.manager.save(
          CatalogProductFlat,
          catalogProductInFlatTable,
        );
      }

      console.timeEnd('flat table');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to save product in flat table', err);
      throw new InternalServerErrorException(
        'Failed to save product in flat table',
      );
    }
  }

  private async mapperForProductsArray(products: any[]) {
    try {
      let mappedData = await Promise.all(
        products.map(async (e) => {
          // console.log(e, 'ITEM');
          // console.log(e.parentRelation, 'This is mapper product links');
          let data = {
            id: e.catalogProduct.id,
            sku: e.catalogProduct.sku,
            status: e.catalogProduct.status,
            type_id: e.catalogProduct.type_id,
            created_at: e.catalogProduct.created_at,
            updated_at: e.catalogProduct.updated_at,
            product_links: await this.productAssociationMapperHelper(
              e.parentRelation,
            ),
            // attributes_list: await this.attributesListMapperHelper(e.attributes),
            attributes_list: await e.attributes.map((e) => {
              return { attribute_code: e.attribute.code, value: e.value };
            }),
            inventory_details: {
              qty: e.inventoryValues?.qty,
              is_in_stock: e.inventoryValues?.is_in_stock,
              min_sale_qty: e.inventoryValues?.min_sale_qty,
              max_sale_qty: e.inventoryValues?.max_sale_qty,
              backorders: e.inventoryValues?.backorders == true ? 1 : 0,
            },
            tier_prices: await (e.tierPriceValues || []).map((e) => {
              return { qty: e?.quantity, value: e?.value };
            }),
            category_associated: await (
              e.productCategoryRelationValues || []
            ).map((e) => e?.category?.id),
            media_gallery_entries: await (e.media_gallery_entries || []).map(
              (e) => {
                return {
                  value: e?.value,
                  is_disabled: e?.is_disabled,
                  position: e?.position,
                  url: e?.url,
                  title: e?.title,
                  description: e?.description,
                };
              },
            ),
          };
          return data;
        }),
      );
      return mappedData;
    } catch (err) {
      this.logger.error('Failed to map products in correct format', err);
      throw new BadRequestException('Failed to map products in correct format');
    }
  }

  private async mapperForProductsFromFlatTable(products: any[], labels?: any) {
    try {
      if (labels.showOptionsValues === true) {
        let ids = [];

        products.forEach((e) => {
          // let ids = []
          ids.push(
            e.catalogProductFlatRelations?.manufacturer,
            e.catalogProductFlatRelations?.visibility,
            e.catalogProductFlatRelations?.tax_class_id,
            e.catalogProductFlatRelations?.dispatch_days,
            Number(e.catalogProductFlatRelations?.country_of_manufacture),
          );
        });

        ids = ids.filter((x) => x !== null && x !== undefined && !isNaN(x));
        let uniqueOptions = [...new Set(ids)];

        const fetchOptions = await this.productAttributeOptionsRepository.find({
          where: { id: In(uniqueOptions) },
        });

        var OptionsIdValueMap = new Map();
        fetchOptions.forEach((e) => {
          OptionsIdValueMap.set(e.id, e.value);
        });
      }

      let product_ids = products.map((e) => {
        return e.id;
      });

      const faqs = await this.productFaqRepository.find({
        relations: ['product'],
        where: { product: { id: In(product_ids) } },
        select: ['product_id'],
      });

      const faqsMap = new Map<number, boolean>();
      faqs.forEach((faq) => {
        faqsMap.set(faq.product_id, true); // Mark as true if FAQ exists
      });

      // Mark products with no FAQ as false
      product_ids.forEach((productId) => {
        if (!faqsMap.has(productId)) {
          faqsMap.set(productId, false);
        }
      });

      let mappedData = await Promise.all(
        products.map(async (e) => {
          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.manufacturer
          ) {
            let optionsKey = e.catalogProductFlatRelations.manufacturer;

            e.catalogProductFlatRelations.manufacturer =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.visibility
          ) {
            let optionsKey = e.catalogProductFlatRelations.visibility;

            e.catalogProductFlatRelations.visibility =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.dispatch_days
          ) {
            let optionsKey = e.catalogProductFlatRelations.dispatch_days;

            e.catalogProductFlatRelations.dispatch_days =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.tax_class_id
          ) {
            let optionsKey = e.catalogProductFlatRelations.tax_class_id;

            e.catalogProductFlatRelations.tax_class_id =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.country_of_manufacture
          ) {
            let optionsKey =
              e.catalogProductFlatRelations.country_of_manufacture;

            e.catalogProductFlatRelations.country_of_manufacture =
              OptionsIdValueMap.get(Number(optionsKey)) || null;
          }

          let mediaData = [
            ...e.productImageRelation,
            ...e.productVideoRelation,
          ];

          if (mediaData.length > 0) {
            mediaData.sort((a, b) => a.position - b.position);

            mediaData = mediaData.map((item) => {
              if (item.value.startsWith('s3/')) {
                item.value = item.value.replace(/^s3\//, '');
                item.value = `${env.aws.bucketBaseUrl}${item.value}`;
              } else if (!item.value.startsWith('http')) {
                item.value = `${env.magentoBaseImagesUrl}/${item.value}`;
              }

              return item;
            });
          }

          let data = {
            id: e.id,
            sku: e.sku,
            status: e.status,
            type_id: e.type_id,
            created_at: e?.created_at,
            updated_at: e?.updated_at,
            completion_percentage: await this.calculateCompletionPercentage(
              e,
              faqsMap,
            ),
            product_links: await this.productAssociationMapperHelper(
              e.parentRelations,
            ),
            attributes_list: { ...e.catalogProductFlatRelations },
            inventory_details: {
              qty: e.inventoryAttributesRelations?.qty,
              is_in_stock: e.inventoryAttributesRelations?.is_in_stock,
              min_sale_qty: e.inventoryAttributesRelations?.min_sale_qty,
              max_sale_qty: e.inventoryAttributesRelations?.max_sale_qty,
              backorders: e.inventoryAttributesRelations?.backorders,
            },
            tier_prices: await (e.tierPricesRelations || []).map((e) => {
              return {
                qty: e?.quantity,
                value: e?.value,
                customer_group: e?.customer_group,
                price_type: e?.price_type,
              };
            }),
            group_tier_price:
              labels?.showGroupedTierPrices === true
                ? await this.fetchTierPricesForGroupedProducts(e)
                : [],
            category_associated:
              labels?.showCategoryNames === true
                ? await (e.productCategoryRelations || []).map(
                    (e) => e?.category?.name,
                  )
                : await (e.productCategoryRelations || []).map(
                    (e) => e?.category?.id,
                  ),
            media_gallery_entries:
              mediaData.length > 0
                ? mediaData.map((e) => {
                    return {
                      id: e?.id,
                      value: e?.value,
                      is_disabled: e?.is_disabled,
                      position: e?.position,
                      url: e?.url,
                      title: e?.title,
                      description: e?.description,
                      image_tags: e?.image_tags,
                    };
                  })
                : [],
          };
          return data;
        }),
      );

      return mappedData;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to map products in correct format', err);
      throw new BadRequestException('Failed to map products in correct format');
    }
  }

  private async mapperForProductsFromFlatTableForCSV(
    products: any[],
    labels?: any,
  ) {
    try {
      if (labels.showOptionsValues === true) {
        let ids = [];

        products.forEach((e) => {
          // let ids = []
          ids.push(
            e.catalogProductFlatRelations?.manufacturer,
            e.catalogProductFlatRelations?.visibility,
            e.catalogProductFlatRelations?.tax_class_id,
            e.catalogProductFlatRelations?.dispatch_days,
            Number(e.catalogProductFlatRelations?.country_of_manufacture),
          );
        });

        ids = ids.filter((x) => x !== null && x !== undefined && !isNaN(x));
        let uniqueOptions = [...new Set(ids)];

        const fetchOptions = await this.productAttributeOptionsRepository.find({
          where: { id: In(uniqueOptions) },
        });

        var OptionsIdValueMap = new Map();
        fetchOptions.forEach((e) => {
          OptionsIdValueMap.set(e.id, e.value);
        });
      }

      let mappedData = await Promise.all(
        products.map(async (e) => {
          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.manufacturer
          ) {
            let optionsKey = e.catalogProductFlatRelations.manufacturer;

            e.catalogProductFlatRelations.manufacturer =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.visibility
          ) {
            let optionsKey = e.catalogProductFlatRelations.visibility;

            e.catalogProductFlatRelations.visibility =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.dispatch_days
          ) {
            let optionsKey = e.catalogProductFlatRelations.dispatch_days;

            e.catalogProductFlatRelations.dispatch_days =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.tax_class_id
          ) {
            let optionsKey = e.catalogProductFlatRelations.tax_class_id;

            e.catalogProductFlatRelations.tax_class_id =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (
            labels.showOptionsValues === true &&
            e.catalogProductFlatRelations?.country_of_manufacture
          ) {
            let optionsKey =
              e.catalogProductFlatRelations.country_of_manufacture;

            e.catalogProductFlatRelations.country_of_manufacture =
              OptionsIdValueMap.get(Number(optionsKey)) || null;
          }

          let data = {
            id: e.id,
            sku: e.sku,
            status: e.status,
            type_id: e.type_id,
            created_at: e?.created_at,
            updated_at: e?.updated_at,
            attributes_list: { ...e.catalogProductFlatRelations },
            inventory_details: {
              qty: e.inventoryAttributesRelations?.qty,
              is_in_stock: e.inventoryAttributesRelations?.is_in_stock,
              min_sale_qty: e.inventoryAttributesRelations?.min_sale_qty,
              max_sale_qty: e.inventoryAttributesRelations?.max_sale_qty,
              backorders: e.inventoryAttributesRelations?.backorders,
            },
            category_associated:
              labels?.showCategoryNames === true
                ? await (e.productCategoryRelations || []).map(
                    (e) => e?.category?.name,
                  )
                : await (e.productCategoryRelations || []).map(
                    (e) => e?.category?.id,
                  ),
          };
          return data;
        }),
      );

      return mappedData;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to map products in correct format', err);
      throw new BadRequestException('Failed to map products in correct format');
    }
  }

  private async mapperForListProductsAPI(products: any[], labels?: any) {
    try {
      let ids = [];

      products.forEach((e) => {
        // let ids = []
        ids.push(
          e.catalogProductFlatRelations?.manufacturer,
          e.catalogProductFlatRelations?.visibility,
          e.catalogProductFlatRelations?.tax_class_id,
          e.catalogProductFlatRelations?.dispatch_days,
          Number(e.catalogProductFlatRelations?.country_of_manufacture),
        );
      });

      ids = ids.filter((x) => x !== null && x !== undefined && !isNaN(x));
      let uniqueOptions = [...new Set(ids)];

      const fetchOptions = await this.productAttributeOptionsRepository.find({
        where: { id: In(uniqueOptions) },
      });

      var OptionsIdValueMap = new Map();
      fetchOptions.forEach((e) => {
          OptionsIdValueMap.set(e.id, e.value);
        });

      let product_ids = products.map((e) => {
        return e.id;
      });

      const faqs = await this.productFaqRepository.find({
        relations: ['product'],
        where: { product: { id: In(product_ids) } },
        select: ['product_id'],
      });

      const faqsMap = new Map<number, boolean>();
      faqs.forEach((faq) => {
        faqsMap.set(faq.product_id, true); // Mark as true if FAQ exists
      });

      // Mark products with no FAQ as false
      product_ids.forEach((productId) => {
        if (!faqsMap.has(productId)) {
          faqsMap.set(productId, false);
        }
      });

      let mappedData = await Promise.all(
        products.map(async (e) => {
          if (e.catalogProductFlatRelations?.manufacturer) {
            let optionsKey = e.catalogProductFlatRelations.manufacturer;

            e.catalogProductFlatRelations.manufacturer =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (e.catalogProductFlatRelations?.visibility) {
            let optionsKey = e.catalogProductFlatRelations.visibility;

            e.catalogProductFlatRelations.visibility =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (e.catalogProductFlatRelations?.dispatch_days) {
            let optionsKey = e.catalogProductFlatRelations.dispatch_days;

            e.catalogProductFlatRelations.dispatch_days =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (e.catalogProductFlatRelations?.tax_class_id) {
            let optionsKey = e.catalogProductFlatRelations.tax_class_id;

            e.catalogProductFlatRelations.tax_class_id =
              OptionsIdValueMap.get(optionsKey) || null;
          }

          if (e.catalogProductFlatRelations?.country_of_manufacture) {
            let optionsKey =
              e.catalogProductFlatRelations.country_of_manufacture;

            e.catalogProductFlatRelations.country_of_manufacture =
              OptionsIdValueMap.get(Number(optionsKey)) || null;
          }

          let mediaData = [
            ...e.productImageRelation,
            // ...e.productVideoRelation,
          ];

          if (mediaData.length > 0) {
            mediaData = mediaData.map((item) => {
              if (item?.value?.startsWith('s3/')) {
                item.value = item?.value.replace(/^s3\//, '');
                item.value = `${env.aws.bucketBaseUrl}${item.value}`;
              } else if (!item?.value?.startsWith('http')) {
                item.value = `${env.magentoBaseImagesUrl}/${item.value}`;
              }

              return item;
            });
          }

          let data = {
            id: e.id,
            sku: e.sku,
            status: e.status,
            type_id: e.type_id,
            created_at: e?.created_at,
            updated_at: e?.updated_at,
            completion_percentage: await this.calculateCompletionPercentage(
              e,
              faqsMap,
            ),

            attributes_list: { ...e.catalogProductFlatRelations },
            inventory_details: {
              qty: e.inventoryAttributesRelations?.qty,
              is_in_stock: e.inventoryAttributesRelations?.is_in_stock,
              min_sale_qty: e.inventoryAttributesRelations?.min_sale_qty,
              max_sale_qty: e.inventoryAttributesRelations?.max_sale_qty,
              backorders: e.inventoryAttributesRelations?.backorders,
            },
            category_associated: await (e.productCategoryRelations || []).map(
              (e) => e?.category?.name,
            ),
            media_gallery_entries:
              mediaData.length > 0
                ? mediaData.map((e) => {
                    return {
                      id: e?.id,
                      value: e?.value,
                      // is_disabled: e?.is_disabled,
                      // position: e?.position,
                      // url: e?.url,
                      // title: e?.title,
                      // description: e?.description,
                      image_tags: e?.image_tags,
                    };
                  })
                : [],
          };
          return data;
        }),
      );

      return mappedData;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to map products in correct format', err);
      throw new BadRequestException('Failed to map products in correct format');
    }
  }

  private async productAssociationMapperHelper(data: any[]) {
    const result: any = {};

    for (const item of data) {
      const { relation_type, child, position } = item;
      // const { id: product_id } = child;

      const product_id = child !== null ? child.id : null;

      if (!result[relation_type]) {
        result[relation_type] = [];
      }

      result[relation_type].push({ product_id, position });
    }

    return result;
  }

  private async buildSku(product) {
    try {
      let manufacturer;
      let manufacturerOptions = product.attributes_list.find(
        (attr) => attr.attribute_code === 'manufacturer',
      )?.value;
      if (manufacturerOptions) {
        let manufacturerData =
          await this.productAttributeOptionsRepository.findOne({
            where: {
              id: manufacturerOptions,
              attribute: { code: 'manufacturer' },
            },
            relations: ['attribute'],
          });
        manufacturer = manufacturerData.value;
      } else {
        manufacturer = 'XXX';
      }

      // let manufacturer = manufacturerData.value;
      manufacturer = manufacturer.replace(/\s+/g, '');
      manufacturer = manufacturer.replace(/[^A-Za-z0-9\-]/g, '');
      const prefix = (manufacturer.substring(0, 3).toUpperCase() + 'X').padEnd(
        5,
        'X',
      );
      const incrementStartsFrom = 100;
      const length = 10;
      let skuToAssign = '';

      let findLastSimilarSKU = await this.catalogProductRepository.findOne({
        where: { sku: Like(`${prefix}%`) },
        order: { created_at: 'DESC' },
      });

      if (findLastSimilarSKU) {
        const findLastIncrement = parseInt(findLastSimilarSKU.sku.substring(5));
        const nextIncrement = findLastIncrement + 1;
        // const incrementLength = String(nextIncrement).length + prefix.length;

        skuToAssign = prefix + String(nextIncrement).padStart(length - 5, '0');
      } else {
        skuToAssign =
          prefix + incrementStartsFrom.toString().padStart(length - 5, '0');
      }

      return skuToAssign;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to generate sku for the product', err);
      throw new BadRequestException('Failed to generate sku for the product');
    }
  }

  async syncProductwithElasticSearch(product: any, eventIdArray: number[]) {
    try {
      const productLinks = await this.productLinksFetchData([product]);

      product.product_links = productLinks;

      const modifiedData = await modifySqlDataForElasticSearch([product]);

      modifiedData['outbox_event_ids'] = eventIdArray;

      // console.log(JSON.stringify(modifiedData), 'MF');

      const response =
        await this.externalApiHelper.sendProductsInElastisticSearchEventBus(
          modifiedData,
        );

      // console.log('Response:', response.data);

      return {
        statusCode: response.status,
        body: JSON.stringify(response.data),
      };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to sync data with elastic search', err);
      throw new InternalServerErrorException(
        'Failed to sync data with elastic search',
      );
    }
  }

  async syncProductwithMongo(product: any, eventIdArray: number[]) {
    try {
      const productLinks = await this.productLinksFetchData([product]);

      product.product_links = productLinks;

      const modifiedData = await modifySqlDataForElasticSearch([product]);

      modifiedData['outbox_event_ids'] = eventIdArray;

      const findUser = await this.eventsOutboxRepository.findOne({
        where: { id: eventIdArray[0] },
        relations: ['activity_logs.activity'],
        select: {
          id: true,
          activity_logs: {
            id: true,
            activity: {
              id: true,
              user: true,
            },
          },
        },
      });

      modifiedData['user'] = findUser
        ? findUser.activity_logs?.activity?.user
        : 'catalog-service';

      const getParents = await this.catalogProductRelationsRepository.find({
        where: {
          child: { id: product.id },
          relation_type: 'associated',
        },
        relations: ['child'],
      });

      let getParentsArray = [];
      let findParentsDetail;

      if (getParents.length > 0) {
        getParents.map((e) => {
          getParentsArray.push(String(e.id));
        });

        findParentsDetail = await this.catalogProductFlatRepository.findOne({
          where: { product: { id: Number(getParentsArray[0]) } },
          select: { id: true, thumbnail: true, url_key: true },
          relations: ['product'],
        });
      }

      modifiedData['parent_product_ids'] =
        getParents.length > 0 ? getParentsArray : [];

      modifiedData['parent_image_url'] =
        getParents.length > 0 ? findParentsDetail?.thumbnail : '';

      modifiedData['parent_url_key'] =
        getParents.length > 0 ? findParentsDetail?.url_key : '';

      // console.log(JSON.stringify(modifiedData), 'MF');

      const response = await this.externalApiHelper.sendProductsInMongoEventBus(
        modifiedData,
        eventIdArray,
      );

      // console.log('Response1', response.data);

      return {
        statusCode: response.status,
        body: JSON.stringify(response.data),
      };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to sync data with mongodb', err);
      throw new InternalServerErrorException(
        'Failed to sync data with mongodb',
      );
    }
  }

  private async productLinksFetchData(data: any) {
    const { product_links } = data[0];

    if (!product_links) return null;

    const allProductIds = Object.values(product_links)
      .flat()
      .map((link: any) => link.product_id);

    const allProducts = await this.catalogProductFindMethod({
      where: { id: In(allProductIds) },
    });

    // Define a function to map product details to the desired structure
    const mapProductDetails = (
      productId: number,
      linkType: string,
      position: number,
    ) => {
      const product = allProducts.find((p) => p.id === productId);
      if (!product) return null;
      return {
        sku: data[0].sku,
        link_type: linkType,
        linked_product_sku: product.sku,
        linked_product_type: product.type_id,
        position: position,
      };
    };

    // console.log(Object.entries(product_links), 'OEP');

    const productLinks = data.product_links as {
      [key: string]: { product_id: number; position: number }[];
    };
    const mappedProductLinks = Object.entries(product_links)
      .flatMap(([linkType, links]: [string, any[]]) => {
        return links.map((link, index) =>
          mapProductDetails(link.product_id, linkType, link.position || index),
        );
      })
      .filter(Boolean);

    return mappedProductLinks;
  }

  async productDetailsAttributeGroupWise(id: number) {
    try {
      const catalogProductValue = await this.catalogProductFindOneMethod({
        where: { id },
      });

      const parentRelation = await this.catalogProductRelationsFindMethod({
        where: { parent: { id } },
        relations: ['child'],
      });

      const integerAttributeValues = await this.intAttributeValuesFindMethod({
        where: { product: { id } },
        relations: ['attribute'],
      });

      const booleanAttributeValues =
        await this.booleanAttributeValuesFindMethod({
          where: { product: { id } },
          relations: ['attribute'],
        });

      const decimalAttributeValues = await this.decimalatributeValuesFindMethod(
        {
          where: { product: { id } },
          relations: ['attribute'],
        },
      );

      const dateAttributeValues = await this.dateAttributeValuesFindMethod({
        where: { product: { id } },
        relations: ['attribute'],
      });
      const stringAttributeValues = await this.stringAttributeValuesFindMethod({
        where: { product: { id } },
        relations: ['attribute'],
      });

      const tierPriceValues = await this.tierPricesFindMethod({
        where: { product: { id } },
      });

      const inventoryValues = await this.inventoryAttributesFindMethod({
        where: { product: { id } },
      });

      const productCategoryRelationValues =
        await this.productCategoryRelationFindMethod({
          where: { product: { id } },
          relations: ['category'],
        });

      const mediaGallaryValues = await this.mediaGallaryFindMethod({
        where: { product: { id } },
      });

      const mediaGallaryVideosValues = await this.mediaGallaryVideoFindMethod({
        where: { product: { id } },
      });

      const productData = {
        catalogProduct: catalogProductValue,
        parentRelation,
        attributes: [
          integerAttributeValues,
          booleanAttributeValues,
          dateAttributeValues,
          stringAttributeValues,
          decimalAttributeValues,
        ].flat(),
        tierPriceValues,
        inventoryValues,
        productCategoryRelationValues,
        media_gallery_entries: [
          ...mediaGallaryValues,
          ...mediaGallaryVideosValues,
        ],
      };

      let transformedData: any = await this.mapperForProductsArray([
        productData,
      ]);
      const attributesGroupData = await this.getAttributeRelationsData();

      let attributesList: any[] = transformedData[0].attributes_list;

      let desiredData = Object.values(attributesGroupData).map((item: any) => {
        let group = item.group;
        let attributes_list = item.attributes.map((attr) => {
          let matchedAttribute = attributesList.find(
            (x) => x.attribute_code === attr.code,
          );
          return {
            attribute_code: attr.code,
            value: matchedAttribute ? matchedAttribute.value : null,
            frontend_input: attr.frontend_input,
            is_required: attr.is_required,
            is_editable: attr.is_editable,
          };
        });

        return {
          group_name: group.group_name,
          attributes_list: attributes_list,
        };
      });

      delete transformedData[0].attributes_list;
      transformedData[0].attributes_details = desiredData;

      return transformedData;
    } catch (error) {
      this.logger.error('Failed to retrieve the requested product', error);
      throw new InternalServerErrorException(
        'Failed to retrieve the requested product',
      );
    }
  }

  //SEARCH FUNCTIONS TO FIND DATA FROM REPOSITORY

  private async productAttributesFindOneBy(body: any) {
    try {
      const data = await this.productAttributesNameRepository.findOneBy(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async productAttributesFindOne(body: any) {
    try {
      const data = await this.productAttributesNameRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async productAttributesFind(body?: any) {
    try {
      const data = await this.productAttributesNameRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async catalogProductFindMethod(body?: any) {
    try {
      const data = await this.catalogProductRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async catalogProductFindOneMethod(body?: any) {
    try {
      const data = await this.catalogProductRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async catalogProductRelationsFindMethod(body: any) {
    try {
      const data = await this.catalogProductRelationsRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async intAttributeValuesFindMethod(body: any) {
    try {
      const data = await this.integerAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async intAttributeValuesFindOneMethod(body: any) {
    try {
      const data = await this.integerAttributeValuesRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async booleanAttributeValuesFindMethod(body: any) {
    try {
      const data = await this.booleanAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async dateAttributeValuesFindMethod(body: any) {
    try {
      const data = await this.dateAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async decimalatributeValuesFindMethod(body: any) {
    try {
      const data = await this.decimalAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async stringAttributeValuesFindMethod(body: any) {
    try {
      const data = await this.stringAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async textAttributeValuesFindMethod(body: any) {
    try {
      const data = await this.textAttributeValuesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async tierPricesFindMethod(body: any) {
    try {
      const data = await this.tierPricesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async inventoryAttributesFindMethod(body: any) {
    try {
      const data = await this.inventoryAttributesRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async inventoryAttributesFindOneMethod(body: any) {
    try {
      const data = await this.inventoryAttributesRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async productCategoryRelationFindMethod(body: any) {
    try {
      const data = await this.productCategoryRelationRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async mediaGallaryFindMethod(body: any) {
    try {
      const data = await this.mediaGallaryRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async mediaGallaryVideoFindMethod(body: any) {
    try {
      const data = await this.mediaGallaryVideoRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async attributesGroupFindOneByMethod(body: any) {
    try {
      const data = await this.attributesGroupRepository.findOneBy(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async attributesGroupFindMethod(body?: any) {
    try {
      const data = await this.attributesGroupRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async attributesGroupFindOneMethod(body: any) {
    try {
      const data = await this.attributesGroupRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async attributesGroupRelationsFindOneMethod(body: any) {
    try {
      const data = await this.attributesGroupRelationRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async productAttributesOptionsFindOneMethod(body: any) {
    try {
      const data = await this.productAttributeOptionsRepository.findOne(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  private async productAttributesOptionsFindMethod(body: any) {
    try {
      const data = await this.productAttributeOptionsRepository.find(body);
      // console.log(data);
      return data;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }

  async bulkUpdateAttributesviaCSV(filePath, file: any, headers: any) {
    try {
      let results = [];
      let csvData = [];
      let failedUpdates = [];
      let createdProducts = [];
      let updatedProducts = [];

      const uploadedFilename = `products_data_${formatDateString(
        new Date(),
      )}.csv`;

      // Upload original CSV file to S3
      let originalCsvUrl = '';
      try {
        // Upload the CSV to S3 with a consistent naming pattern
        const originalCsvFilename = `original-csv/${Date.now()}_${uploadedFilename}`;
        const csvReadStream = fs.createReadStream(filePath);
        originalCsvUrl = await this.s3Service.uploadFileToS3(originalCsvFilename, csvReadStream);
        this.logger.debug(`Original CSV uploaded to S3: ${originalCsvUrl}`);
      } catch (error) {
        this.logger.error(`Failed to upload original CSV to S3: ${error.message}`, error);
        // Continue with processing even if S3 upload fails
      }

      // Create a new record for tracking this bulk update
      const newRecord = new BulkAttributesUpdateStatus();
      newRecord.status = AttributesUpdateStatus.PENDING;
      newRecord.filename = uploadedFilename;
      newRecord.original_file_url = originalCsvUrl; // Save the S3 URL of the original file

      // Save the record
      const savedRecord = await this.bulkAttributesUpdateRepository.save(newRecord);
      

      // Parse CSV data
      const csvParsingResult = await new Promise<{ headers: any[]; csvData: any[] }>((resolve, reject) => {
        fs.createReadStream(filePath)
          .pipe(csvParser())
          .on('headers', (headers) => {
            results = headers;
          })
          .on('data', (data) => {
            csvData.push(data);
          })
          .on('end', () => {
            resolve({ headers: results, csvData });
          })
          .on('error', (error) => {
            reject(error);
          });
      });

      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'bulk_attributes_import',
        user_meta_info: user_data,
      };

      let activityData = await this.createActivity(createActivityBody);

      // Keys that should go to inventory_details, not attributes_list
      const inventoryKeys = [
        'qty',
        'min_sale_qty',
        'backorders',
        'is_in_stock',
        'max_sale_qty',
      ];

        // Keys that should be skipped completely (not used in update)
        // Also skip category_associated as it will be handled separately
        const skipKeys = ['id', 'url_key', 'category_associated'];

      const processedIds = new Set();

      for await (let row of csvData) {
        // Process the row data
        let attributesList = [];
        let inventoryAttributesObject = {};

        // Keys that should be treated as boolean values
        const booleanKeys = [
          'is_cod',
          'is_in_stock',
          'backorders',
          'demo_available',
          'international_active',
        ];

        // Define keys that need special handling
        const dateKeys = ['special_from_date', 'special_to_date', 'pd_expiry_date'];
        const integerKeys = [
          'manufacturer',
          'status',
          'visibility',
          'tax_class_id',
          'featured',
          'reward_point_product',
          'quantity_and_stock_status',
          'rating_count',
          'return_period',
          'dispatch_days'
        ];

        // Handle duplicate keys by normalizing them
        const normalizedData: Record<string, any> = {};

        // First pass: collect all keys and normalize them
        Object.entries(row).forEach(([key, value]) => {
          // Normalize key to handle duplicates (convert to lowercase and singular form)
          const normalizedKey = key.toLowerCase()
            .replace('specifications', 'specification')
            .trim();

          // Skip empty values
          if (value === '') {
            return;
          }

          // Store with normalized key
          normalizedData[normalizedKey] = value;
        });

        // Parse category IDs if present
        let categoryIds = [];
        if (normalizedData.category_associated && normalizedData.category_associated.trim() !== '') {
          try {
            // Split by comma and convert to integers
            categoryIds = normalizedData.category_associated.split(',')
              .map(id => id.trim())
              .filter(id => id !== '')
              .map(id => {
                const parsedId = parseInt(id);
                if (isNaN(parsedId)) {
                  this.logger.warn(`Invalid category ID in CSV: ${id}`);
                  return null;
                }
                return parsedId;
              })
              .filter(id => id !== null);
            
            this.logger.debug(`Parsed category IDs from CSV: ${JSON.stringify(categoryIds)}`);
          } catch (error) {
            this.logger.error(`Failed to parse category_associated from CSV: ${normalizedData.category_associated}`, error);
          }
        }

        // Process normalized data
        Object.entries(normalizedData).forEach(([key, value]) => {
          // Skip processing if value is empty
          if (value === '') {
            return;
          }

          let convertedValue = value;

          // Handle boolean values
          if (booleanKeys.includes(key) && typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (
              lowerValue === 'true' ||
              lowerValue === 'yes' ||
              lowerValue === '1'
            ) {
              convertedValue = true;
            } else if (
              lowerValue === 'false' ||
              lowerValue === 'no' ||
              lowerValue === '0'
            ) {
              convertedValue = false;
            }
          }

          // Handle date values
          if (dateKeys.includes(key)) {
            try {
              // Special handling for pd_expiry_date to avoid NULL values
              if (key === 'pd_expiry_date') {
                if (!value || value === '' || value === 'null' || value === 'undefined') {
                  // Use a default value for pd_expiry_date (1 year from now)
                  const defaultDate = new Date();
                  defaultDate.setFullYear(defaultDate.getFullYear() + 1);
                  convertedValue = defaultDate;
                  this.logger.warn(`Empty pd_expiry_date, using default value: ${defaultDate}`);
                } else {
                  // Try to parse as date if it's a valid date string
                  const dateValue = new Date(value);
                  if (!isNaN(dateValue.getTime())) {
                    convertedValue = dateValue;
                  } else {
                    // If invalid date, use default
                    const defaultDate = new Date();
                    defaultDate.setFullYear(defaultDate.getFullYear() + 1);
                    convertedValue = defaultDate;
                    this.logger.warn(`Invalid pd_expiry_date: ${value}, using default: ${defaultDate}`);
                  }
                }
              } else if (typeof value === 'string') {
                // For other date fields, try to parse normally
                const dateValue = new Date(value);
                if (!isNaN(dateValue.getTime())) {
                  convertedValue = dateValue;
                }
              }
            } catch (error) {
              // If date parsing fails, provide default for required fields
              if (key === 'pd_expiry_date') {
                const defaultDate = new Date();
                defaultDate.setFullYear(defaultDate.getFullYear() + 1);
                convertedValue = defaultDate;
                this.logger.warn(`Exception parsing pd_expiry_date, using default: ${defaultDate}`, error);
              } else {
                this.logger.warn(`Failed to parse date for ${key}: ${value}`, error);
              }
            }
          }
          
          // Handle integer values
          if (integerKeys.includes(key) && typeof value === 'string') {
            try {
              // Convert string to integer
              const intValue = parseInt(value.trim());
              if (!isNaN(intValue)) {
                convertedValue = intValue;
                this.logger.debug(`Converted ${key} from string '${value}' to integer ${intValue}`);
              }
            } catch (error) {
              // If integer parsing fails, keep original value
              this.logger.warn(`Failed to parse integer for ${key}: ${value}`);
            }
          }

          // Handle inventory attributes
          if (inventoryKeys.includes(key)) {
            inventoryAttributesObject[key] = convertedValue;
          }
          // Handle regular attributes (excluding skipped keys)
          else if (!skipKeys.includes(key)) {
            attributesList.push({
              attribute_code: key,
              value: convertedValue,
            });
          }
        });

        // Check if this is a new product (no ID) or update to existing product
        const isNewProduct = !row.id || row.id.trim() === '';
        
        if (isNewProduct) {
          // Create a new product
          try {
            // Prepare data for product creation
            // Required fields for product creation
            const nameAttribute = attributesList.find(attr => attr.attribute_code === 'name');
            const typeIdAttribute = attributesList.find(attr => attr.attribute_code === 'type_id');
            const visibilityAttribute = attributesList.find(attr => attr.attribute_code === 'visibility');
            
            if (!nameAttribute || !typeIdAttribute || !visibilityAttribute) {
              throw new BadRequestException('Missing required attributes: name, type_id or visibility');
            }
            
            // Extract type_id from attributes
            // Default to simple product type as TypeIdValues enum
            let typeId = 'simple' as any; // Will be cast to the correct TypeIdValues type
            
            if (typeIdAttribute) {
              const value = String(typeIdAttribute.value).toLowerCase();
              
              // Validate against known product types
              if (['simple', 'configurable', 'virtual', 'downloadable', 'bundle', 'grouped'].includes(value)) {
                typeId = value as any; // Will be cast to the correct TypeIdValues type
              } else {
                this.logger.warn(`Invalid product type '${value}' in CSV, defaulting to 'simple'`);
              }
            }
            
            // Prepare product creation body
            // Create product with required fields matching CreateCatalogProductDto
            // Set default inventory values
            const defaultInventory = {
              is_in_stock: false,
              qty: 0,
              min_sale_qty: 1,
              max_sale_qty: 100,
              backorders: false
            };
            
            // Override with any values provided in the CSV
            if (Object.keys(inventoryAttributesObject).length > 0) {
              Object.assign(defaultInventory, inventoryAttributesObject);
            }
            
            // Prepare a clean attribute list with all required attributes
            // Make sure name attribute is included
            if (!attributesList.some(attr => attr.attribute_code === 'name') && nameAttribute) {
              attributesList.push({
                attribute_code: 'name',
                value: nameAttribute.value
              });
            }
            
            // Make sure visibility attribute is included
            if (!attributesList.some(attr => attr.attribute_code === 'visibility') && visibilityAttribute) {
              attributesList.push({
                attribute_code: 'visibility',
                value: parseInt(String(visibilityAttribute.value)) || 4 // Default to catalog & search if not valid
              });
            }

            // Create DTO with only properties that exist in the type definition
            const createProductDto = {
              id: undefined, // Will be auto-generated
              type_id: typeId,
              attribute_set_id: 4, // Default attribute set
              status: true, // Enabled status (boolean type)
              inventory_details: defaultInventory,
              category_associated: categoryIds, // Use parsed category IDs from CSV
              product_links: {} as any, // Empty object for product links
              tier_prices: [], // Empty array for tier prices
              attributes_list: attributesList // Use all attributes including visibility
            } as CreateCatalogProductDto; // Cast to the expected type
            
            try {
              // Create the product
              const createdProduct = await this.createCatalogProduct(createProductDto, headers);

              // Add to created products list
              createdProducts.push({
                id: createdProduct.id,
                sku: createdProduct.sku
              });
              this.logger.debug(`Created new product from CSV import: ${createdProduct.id}`);
            } catch (error) {
              // Get product identifier for the failed row - either name or a fallback
              const productName = attributesList.find(attr => attr.attribute_code === 'name')?.value || 'Unknown';
              
              // Add to failed updates list with a meaningful identifier
              failedUpdates.push(`create_${productName}`);
              
              // Log detailed error but continue processing other rows
              this.logger.error(`Failed to create product '${productName}' from CSV: ${error.message}`, error);
            }
          } catch (error) {
            // Log and track creation failures
            failedUpdates.push('new_product');
            this.logger.error(`Failed to create product from CSV: ${error.message}`, error);
          }
        } else {
          // Skip if ID has already been processed
          if (processedIds.has(row.id)) {
            continue;
          }
          
          // Mark the ID as processed
          processedIds.add(row.id);
          
          let attributes_list_data: any = {
            attributes_list: attributesList,
            updated_by_action_details: {
              action_type: 'bulk_attributes_import',
              activity: activityData,
            },
          };
          
  
          const statusAttribute = attributesList.find(
            (attr) => attr.attribute_code === 'status',
          );

          if (statusAttribute) {
            attributes_list_data['status'] = statusAttribute.value;
          }

          if (Object.keys(inventoryAttributesObject).length > 0) {
            attributes_list_data['inventory_details'] = inventoryAttributesObject;
          }

          let idNumber = parseInt(row.id);

          try {
            const data = await this.updateCatalogProduct(
              attributes_list_data,
              idNumber,
              headers,
            );
            attributes_list_data = {
              status: data.status,
              inventory_details: data.inventory_details,
              attributes_list: data.attributes_list,
              category_associated: categoryIds,
            };
            
            // Track successful update
            updatedProducts.push(idNumber);
            
            // Log successful update
            this.logger.debug(`Successfully updated product with id ${idNumber}`);
          } catch (error) {
            // Add detailed error information
            failedUpdates.push(`update_${idNumber}`);
            
            // Get more details about the product for better error reporting
            const nameAttribute = attributesList.find(attr => attr.attribute_code === 'name');
            const productName = nameAttribute ? nameAttribute.value : 'Unknown';
            
            this.logger.error(`Failed to update product ID ${idNumber} (${productName}): ${error.message}`, error);
          }
        }
      }

      // Clean up the uploaded file after processing
      fs.unlink(`./uploads/${file.originalname}`, (err) => {
        if (err) {
          this.logger.error(`Error deleting temporary upload file: ${err.message}`, err);
        } else {
          this.logger.debug('Temporary uploaded file deleted successfully');
        }
      });
      
      // Handle failed updates by creating a report file
      if (failedUpdates.length > 0) {
        const directoryPath = './reports';
        const filename = `failed_products_update_${Date.now()}.csv`;
        const uploadedErrorFilename = `failed_products_update_${formatDateString(new Date())}.csv`;

        // Ensure reports directory exists
        if (!fs.existsSync(directoryPath)) {
          fs.mkdirSync(directoryPath, { recursive: true });
        }

        const filepath = `${directoryPath}/${filename}`;

        // Create a promise to handle the file creation and upload process
        await new Promise<void>((resolve, reject) => {
          const writeStream = fs.createWriteStream(filepath);
          
          // Write CSV headers
          const headers = ['failed_product_ids'];
          writeStream.write(`${headers.join(',')}\n`);
          
          // Write each failed update ID
          failedUpdates.forEach(productId => {
            writeStream.write(`${productId}\n`);
          });
          
          // Handle write stream events
          writeStream.on('error', (error) => {
            this.logger.error(`Error writing failure report: ${error.message}`, error);
            reject(error);
          });
          
          writeStream.on('finish', async () => {
            try {
              // Once the file is written, upload it to S3
              const readStream = fs.createReadStream(filepath);
              
              // Upload the file to S3
              const reportFileURL = await this.s3Service.uploadFileToS3(
                `reports/${uploadedErrorFilename}`,
                readStream
              );
              
              // Update the bulk import record with the report URL
              await this.bulkAttributesUpdateRepository.update(
                { id: savedRecord.id },
                { report_url: reportFileURL, status: AttributesUpdateStatus.COMPLETE }
              );
              
              // Clean up the temporary file
              fs.unlink(filepath, (err) => {
                if (err) {
                  this.logger.error(`Error deleting temporary report file: ${err.message}`, err);
                } else {
                  this.logger.debug('Temporary report file deleted successfully');
                }
              });
              
              resolve();
            } catch (error) {
              this.logger.error(`Error uploading failure report to S3: ${error.message}`, error);
              reject(error);
            }
          });
          
          // End the write stream to trigger the 'finish' event
          writeStream.end();
        }).catch(error => {
          this.logger.error(`Failed to process failure report: ${error.message}`, error);
        });
      }

      // Update the bulk import status even if there were no failures
      if (failedUpdates.length === 0) {
        await this.bulkAttributesUpdateRepository.update(
          { id: savedRecord.id },
          { status: AttributesUpdateStatus.COMPLETE }
        );
      }
      
      return {
        status: failedUpdates.length > 0 ? 'partial' : 'success',
        created: createdProducts.length,
        updated: updatedProducts.length,
        failed: failedUpdates.length,
        message: failedUpdates.length > 0 ? `${failedUpdates.length} products failed to import` : 'All products imported successfully'
      };
    } catch (error) {
      this.logger.error(`Bulk CSV import failed: ${error.message}`, error);
      throw new InternalServerErrorException(`Failed to process CSV import: ${error.message}`);
    }
  }

  async downloadProductCsv(
    product_ids: number[],
    filters: any,
    columns_list: string[],
  ) {
    try {
      const directoryPath = './reports';
      const filename = `products_data_${Date.now()}.csv`;

      const uploadedFilename = `products_data_${formatDateString(
        new Date(),
      )}.csv`;

      const newRecord = new ProductDataCsvUrls();
      newRecord.status = CsvGenerateStatus.PENDING;
      newRecord.filename = uploadedFilename;

      const savedRecord =
        await this.productDataCsvUrlsRepository.save(newRecord);

      if (!fs.existsSync(directoryPath)) {
        fs.mkdirSync(directoryPath, { recursive: true });
      }

      let currentPage = 1;
      const pageSize = 100;

      let pagination = {
        page: currentPage,
        size: pageSize,
      };

      const filepath = `${directoryPath}/${filename}`;
      // console.log(filters);
      // console.log(pagination);

      const allProducts = Object.keys(filters).length !== 0 ? null : [];

      filters['showOptionsValues'] = true;

      // Define columns list for CSV
      const columns_list = [
        'id', 'sku', 'created_at', 'updated_at', 'product_type', 'categories',
        'name', 'description', 'short_description', 'weight', 'status', 'tax_class_id',
        'visibility', 'price', 'special_price', 'special_from_date', 'special_to_date',
        'url_key', 'meta_title', 'meta_keyword', 'meta_description', 'manufacturer',
        'dispatch_days', 'packaging', 'hsn_code', 'pd_expiry_date', 'international_active', 
        'reward_point_product', 'qty', 'backorders'
      ];

      let { items, pages_count } = await this.getSelectedProductDetailsByIds(
        allProducts,
        filters,
        null,
        pagination,
        null,
        columns_list,
      );

      if (items.length === 0) {
        throw new BadRequestException('No products found to generate CSV');
      }

      let counter = 0;

      const writeStream = fs.createWriteStream(filepath);

      // Get ordered headers that will be used for both headers and data rows
      const orderedColumns = await this.writeCSVHeaders(writeStream, columns_list);

      while (currentPage < pages_count + 1) {
        for await (let item of items) {
          counter++;
          console.log('DOWNLOAD CSV EXPORT COUNTER', counter);
          await this.writeRow(
            writeStream,
            {
              id: item?.id,
              sku: item?.sku,
              created_at: item?.created_at
                ? dateInBasicFormat(item.created_at)
                : '',
              updated_at: item?.updated_at
                ? dateInBasicFormat(item.updated_at)
                : '',
              product_type: item?.type_id,
              categories:
                item?.category_associated.length >= 1
                  ? `${item.category_associated.join(', ')}`
                  : '',
              name: item?.attributes_list?.name ?? '',
              description: item?.attributes_list?.description ?? '',
              short_description: item?.attributes_list?.short_description ?? '',
              // weight: item.attributes_list?.weight ?? '',
              weight:
                item?.attributes_list?.weight == null ||
                isNaN(Number(item?.attributes_list?.weight))
                  ? ''
                  : item?.attributes_list.weight,
              status: item?.status == 1 ? 'TRUE' : 'FALSE',
              tax_class_id: item?.attributes_list?.tax_class_id ?? '',
              visibility: item?.attributes_list?.visibility ?? '',
              price:
                item.attributes_list?.price == null ||
                isNaN(Number(item?.attributes_list?.price))
                  ? ''
                  : item?.attributes_list.price,
              special_price:
                item?.attributes_list?.special_price == null ||
                isNaN(Number(item?.attributes_list?.special_price))
                  ? item?.attributes_list?.price
                  : item?.attributes_list?.special_price,
              special_from_date: item.attributes_list.special_from_date
                ? dateInBasicFormat(item?.attributes_list?.special_from_date)
                : '',
              special_to_date: item.attributes_list.special_to_date
                ? dateInBasicFormat(item?.attributes_list?.special_to_date)
                : '',
              url_key: item.attributes_list?.url_key ?? '',
              meta_title: item.attributes_list?.meta_title ?? '',
              meta_keyword: item.attributes_list?.meta_keyword ?? '',
              meta_description: item.attributes_list?.meta_description ?? '',
              manufacturer: item.attributes_list?.manufacturer ?? '',
              dispatch_days: item.attributes_list?.dispatch_days ?? '',
              packaging: item.attributes_list?.packaging ?? '',
              hsn_code: item.attributes_list?.hsn_code ?? '',
              pd_expiry_date:
                dateInBasicFormat(item.attributes_list?.pd_expiry_date) ?? '',
              international_active:
                item.attributes_list?.international_active == 1
                  ? 'Enabled'
                  : 'Disabled',
              reward_point_product:
                item.attributes_list?.reward_point_product ?? '',
              qty: item.inventory_details?.qty ?? '',
              backorders: item.inventory_details?.backorders ?? '',
              // related_products:
              //   `${item.product_links?.related
              //     ?.map((e) => {
              //       return e.product_id;
              //     })
              //     .join(', ')}` ?? '',
              // crosssell_products:
              //   `${item.product_links?.crosssell
              //     ?.map((e) => {
              //       return e.product_id;
              //     })
              //     .join(', ')}` ?? '',
              // associated_products:
              //   `${item.product_links?.associated
              //     ?.map((e) => {
              //       return e.product_id;
              //     })
              //     .join(', ')}` ?? '',
            },
            orderedColumns,
          );
        }

        currentPage++;
        pagination.page = currentPage;

        ({ items } = await this.getSelectedProductDetailsByIds(
          null,
          filters,
          null,
          pagination,
          null,
          columns_list,
        ));
      }

      writeStream.end();
      writeStream.on('finish', async () => {
        // Once the file is written, open a read stream and stream it back to the client
        const readStream = fs.createReadStream(filepath);

        readStream.on('end', () => {
          //deleting the temp report file
          fs.unlink(filepath, (err) => {
            if (err) {
              console.error(err);
              return;
            }
            console.log('temporary report file deleted successfully!');
          });
        });
        try {
          const reportFileURL = await this.s3Service.uploadFileToS3(
            `reports/${uploadedFilename}`,
            readStream,
          );
          // console.log(reportFileURL);

          const savedRecord = await this.productDataCsvUrlsRepository.update(
            { filename: uploadedFilename },
            { status: CsvGenerateStatus.COMPLETE, report_url: reportFileURL },
          );

          // console.log('Report URL', reportFileURL);
          return { fileurl: reportFileURL };
        } catch (e) {
          console.log('Error', e);
        }
      });
    } catch (error) {
      this.logger.error('Failed to generate bulk products data csv', error);
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to generate bulk products data csv',
        );
      }
    }
  }

  private async writeCSVHeaders(
    writeStream: fs.WriteStream,
    columns_list: any,
  ) {
    const headers = [
      'sku',
      'product_type',
      'categories',
      'name',
      'description',
      'short_description',
      'weight',
      'status',
      'tax_class_id',
      'visibility',
      'price',
      'special_price',
      'special_from_date',
      'special_to_date',
      'url_key',
      'meta_title',
      'meta_keyword',
      'meta_description',
      'created_at',
      'updated_at',
      'manufacturer',
      'id',
      'dispatch_days',
      'packaging',
      'hsn_code',
      'pd_expiry_date',
      'international_active',
      'reward_point_product',
      'qty',
      'backorders',
      'related_products',
      'crosssell_products',
      'associated_products',
    ];

    // Always start with these fixed columns
    let column_headers = ['id', 'created_at', 'updated_at'];

    // Add any additional columns from columns_list
    for (const column of columns_list) {
      if (headers.includes(column) && !column_headers.includes(column)) {
        column_headers.push(column);
      }
    }

    // Write the headers
    writeStream.write(`${column_headers.join(',')}\n`);
    
    // Return the ordered column headers so writeRow can use the exact same order
    return column_headers;
  }

  private async writeRow(
    writeStream: fs.WriteStream,
    columns_list: any,
    orderedHeaders, // This parameter now contains exactly the same ordered column headers as written in the CSV headers
  ) {
    const row = {
      id: columns_list.id,
      created_at: columns_list.created_at,
      updated_at: columns_list.updated_at,
      name: columns_list.name,
      sku: columns_list.sku,
      product_type: columns_list.product_type,
      visibility: columns_list.visibility,
      price: columns_list.price,
      categories: columns_list.categories,
      description: columns_list.description,
      short_description: columns_list.short_description,
      weight: columns_list.weight,
      status: columns_list.status,
      tax_class_id: columns_list.tax_class_id,
      special_price: columns_list.special_price || columns_list.price,
      special_from_date: columns_list.special_from_date,
      special_to_date: columns_list.special_to_date,
      url_key: columns_list.url_key,
      meta_title: columns_list.meta_title, 
      meta_keyword: columns_list.meta_keyword,
      meta_description: columns_list.meta_description,
      manufacturer: columns_list.manufacturer,
      dispatch_days: columns_list.dispatch_days,
      packaging: columns_list.packaging,
      hsn_code: columns_list.hsn_code,
      pd_expiry_date: columns_list.pd_expiry_date,
      international_active: columns_list.international_active,
      reward_point_product: columns_list.reward_point_product,
      qty: columns_list.qty,
      backorders: columns_list.backorders,
      related_products: columns_list.related_products,
      crosssell_products: columns_list.crosssell_products,
      associated_products: columns_list.associated_products,
    };

    // Create a new empty array for values
    const valuesArray = [];

    // For each header in the ordered headers, add the corresponding value to the values array
    // This ensures perfect alignment between headers and data
    for (const header of orderedHeaders) {
      let value = row[header];

      if (typeof value === 'string') {
        value = value.replace(/"/g, '""');
        value = `"${value}"`;
      }
      valuesArray.push(value);
    }

    const canContinue = writeStream.write(`${valuesArray.join(',')}\n`);
    if (!canContinue) {
      await new Promise<void>((resolve) => writeStream.once('drain', resolve));
    }
  }

  private async calculateCompletionPercentage(
    product: any,
    faqsMap: Map<number, boolean>,
  ) {
    const valuePercentage = 25;
    let totalPercentage = 0;
    const completeFields = [];
    const incompleteFields = [];

    const catalogProduct = product.catalogProductFlatRelations;

    const fieldsToCheck = [
      { field: catalogProduct?.description, name: 'Description', weight: 5 },
      {
        field: catalogProduct?.short_description,
        name: 'Short Description',
        weight: 10,
      },
      { field: catalogProduct?.warranty, name: 'Warranty', weight: 5 },
      { field: catalogProduct?.packaging, name: 'Packaging', weight: 5 },
      { field: catalogProduct?.htext, name: 'hText', weight: 5 },
      {
        field: catalogProduct?.key_specifications,
        name: 'Key Specifications',
        weight: 10,
      },
      { field: catalogProduct?.features, name: 'Features', weight: 10 },
      {
        field: catalogProduct?.shipping_return,
        name: 'Shipping Return',
        weight: 5,
      },
      {
        field: catalogProduct?.in_the_box,
        name: 'In The Box',
        weight: 10,
      },
      { field: catalogProduct?.usp, name: 'USP', weight: 5 },
    ];

    for (const { field, name, weight } of fieldsToCheck) {
      if (field) {
        totalPercentage += weight;
        completeFields.push(name);
      } else {
        incompleteFields.push(name);
      }
    }

    const hasFaqs = faqsMap?.get(product.id) ?? false;

    if (hasFaqs) {
      totalPercentage += 10;
      completeFields.push('Faqs');
    } else {
      incompleteFields.push('Faqs');
    }

    if (product.productGallaryRelation?.length > 0) {
      // Formula: (numImages / desiredImages) * valuePercentage, capped at valuePercentage
      const numImages = product.productGallaryRelation.length;
      const desiredImages = 5; // This is our benchmark for a 'complete' image set
      const imagePercentage = (numImages / desiredImages) * valuePercentage;
      totalPercentage += Math.min(imagePercentage, valuePercentage);
      completeFields.push('Media Images');
    } else {
      incompleteFields.push('Media Images');
    }

    if (product.productVideoRelation?.length > 0) {
      totalPercentage += 10;
      completeFields.push('Videos');
    } else {
      incompleteFields.push('Videos');
    }

    return {
      percentage: Math.round(totalPercentage),
      complete_fields: completeFields,
      incomplete_fields: incompleteFields,
    };
  }

  async bulkDataCsvList(page_no: number) {
    try {
      let offSet = (page_no - 1) * env.sqlQueryResultsize;
      let page_size = env.sqlQueryResultsize;

      let data = await this.productDataCsvUrlsRepository.findAndCount({
        order: { id: 'DESC' },
        skip: offSet,
        take: page_size,
      });

      let csvData = data[0];
      let csvCount = data[1];

      return {
        item_count: csvCount,
        pages_count: Math.ceil(csvCount / env.sqlQueryResultsize),
        page_no: page_no,
        page_size: env.sqlQueryResultsize,
        data: csvData,
      };
    } catch (error) {
      this.logger.error('Failed to fetch csv list data', error);
      throw new InternalServerErrorException('Failed to fetch csv list data');
    }
  }

  async bulkAttributeUpdateCsvList(page_no: number) {
    try {
      let offSet = (page_no - 1) * env.sqlQueryResultsize;
      let page_size = env.sqlQueryResultsize;

      let data = await this.bulkAttributesUpdateRepository.findAndCount({
        order: { id: 'DESC' },
        skip: offSet,
        take: page_size,
      });

      let csvData = data[0];
      let csvCount = data[1];

      return {
        item_count: csvCount,
        pages_count: Math.ceil(csvCount / env.sqlQueryResultsize),
        page_no: page_no,
        page_size: env.sqlQueryResultsize,
        data: csvData,
      };
    } catch (error) {
      this.logger.error('Failed to fetch bulk update list data', error);
      throw new InternalServerErrorException(
        'Failed to fetch bulk update list data',
      );
    }
  }

  async bulkUpdateSkus(body, headers) {
    try {
      let { sku_list } = body;

      if (sku_list.length > 500) {
        throw new BadRequestException('Only 500 skus can be updated at once.');
      }

      if (!sku_list || sku_list?.length == 0) {
        throw new BadRequestException(
          'No list of skus to perform update action.',
        );
      }

      // Loop through the SKUs to check for invalid items
      sku_list.forEach((sku) => {
        // Check if SKU is undefined or empty
        if (sku === undefined || sku === null || !sku.trim()) {
          throw new BadRequestException('Empty or undefined SKU found.');
        }
      });

      const foundSkusSet = new Set<string>();

      const getProductsData: any = await this.catalogProductRepository.find({
        where: { sku: In(sku_list) },
        relations: ['catalogProductFlatRelations'],
        select: {
          id: true,
          sku: true,
          type_id: true,
          created_at: true,
          updated_at: true,
          catalogProductFlatRelations: {
            id: true,
            manufacturer: true,
          },
        },
      });

      if (!getProductsData) {
        throw new BadRequestException('No products to update SKUs');
      }

      getProductsData.forEach((product) => foundSkusSet.add(product.sku));

      const missingSkus = sku_list.filter((sku) => !foundSkusSet.has(sku));

      if (missingSkus.length > 0) {
        await Promise.all(
          missingSkus.map((sku) =>
            this.skuUpdateRecordRepository.save(
              new SkuUpdateRecord({
                old_sku: sku,
                new_sku: null,
                status: false,
                error: 'SKU not found',
                modified_by: headers.admin_identifier,
              }),
            ),
          ),
        );
      }

      let transformedSkuData = await getProductsData.map((e) => {
        return {
          id: e.id,
          sku: e.sku,
          type_id: e.type_id,
          attributes_list: [
            {
              attribute_code: 'manufacturer',
              value: e.catalogProductFlatRelations.manufacturer,
            },
          ],
        };
      });

      let user_meta_info = {};
      let productKeyObject;
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'bulk_sku_update',
        user_meta_info: JSON.stringify(user_data),
      };

      let activityData, activityLogsData;

      activityData = await this.createActivity(createActivityBody);

      for await (const productData of transformedSkuData) {
        const connection = this.entityManager.connection;
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        try {
          await queryRunner.startTransaction();

          let newSku = await this.buildSku(productData);

          let createActivityLogsBody = {
            entity_id: productData.id,
            old_value: productData.sku
              ? { sku: productData.sku }
              : { sku: null },
            new_value: { sku: newSku },
          };

          productKeyObject = { ...productData };

          delete productKeyObject.attributes_list;

          let skuUpdateRecordBody = new SkuUpdateRecord();
          skuUpdateRecordBody.old_sku = productData.sku;
          skuUpdateRecordBody.new_sku = newSku;
          skuUpdateRecordBody.status = true;
          skuUpdateRecordBody.error = null;
          skuUpdateRecordBody.product = productKeyObject;
          (skuUpdateRecordBody.modified_by = headers.admin_identifier),
            await Promise.all([
              queryRunner.manager.update(
                CatalogProduct,
                {
                  id: productData.id,
                },
                { sku: newSku },
              ),

              queryRunner.manager.update(
                CatalogProductFlat,
                {
                  product: { id: productData.id },
                },
                { sku: newSku },
              ),

              queryRunner.manager.save(SkuUpdateRecord, skuUpdateRecordBody),

              // NOTE: currently commented the below function because it will be usind in production only
              //this.externalApiHelper.updateProductAndSkuInViniculum(fetchUpdatedProduct.items);
            ]);
          activityLogsData = await this.createActivityLog(
            {
              ...createActivityLogsBody,
              activity: activityData,
            },
            queryRunner,
          );

          await this.createEventOutbox(
            {
              entity_id: productData.id,
              entity_type: EntityType.PRODUCT,
              activity_logs: activityLogsData,
            },
            queryRunner,
          );
          await queryRunner.commitTransaction();
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to update sku', err);

          await queryRunner.rollbackTransaction();

          await queryRunner.manager.save(SkuUpdateRecord, {
            old_sku: productData.sku,
            new_sku: null,
            status: false,
            error: err.message.substring(0, 255),
            product: productKeyObject,
            modified_by: headers.admin_identifier,
          });
        } finally {
          await queryRunner.release();
        }
      }

      return { message: 'SKUs updated successfully' };
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to bulk update skus', error);
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException('Failed to update bulk skus');
      }
    }
  }


  private async buildUrlKey(name: string): Promise<string> {
    try {
      // Generate the base URL key
      let baseUrlKey = name.toLowerCase().replace(/[\s()-]+/g, '-');

      // Find existing URL keys that start with the base URL key
      const existingProducts = await this.stringAttributeValuesRepository.find({
        where: {
          attribute: { code: 'url_key' },
          value: Like(`${baseUrlKey}%`),
        },
        relations: ['attribute'],
        order: { value: 'ASC' }, // Sort ascending to find the highest number
      });

      // If there are no existing keys, return the base URL key
      if (existingProducts.length === 0) {
        return baseUrlKey;
      }

      // Find the highest number suffix
      const existingKeys = existingProducts.map((product) => product.value);
      const numberSuffixes = existingKeys
        .map((key) => {
          // Extract suffix after the last hyphen
          const parts = key.split('-');
          const suffix =
            parts.length > 1 ? parseInt(parts[parts.length - 1], 10) : 0;
          return isNaN(suffix) ? 0 : suffix;
        })
        .filter((num) => num > 0);

      // Determine the next suffix to use
      const nextSuffix = Math.max(...numberSuffixes, 0) + 1;

      // Return the URL key with the incremented suffix
      return `${baseUrlKey}-${nextSuffix}`;
    } catch (err) {
      console.error(err);
      this.logger.error('Failed to build URL key', err);
      throw new InternalServerErrorException('Failed to build URL key');
    }
  }

  async createActivity(body: {
    user: string;
    entity: EntityTypeEnum;
    activity_type: string;
    user_meta_info?: any;
  }) {
    try {
      console.time('activity');
      const { user, entity, activity_type, user_meta_info } = body;

      let activityData = {
        user,
        entity,
        activity_type,
        user_meta_info: user_meta_info || null,
      };
      let addActivity = this.activityRepository.create(activityData);
      let saveActivity = await this.activityRepository.save(addActivity);
      console.timeEnd('activity');

      return saveActivity;
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to create activity', error);
      throw new InternalServerErrorException('Failed to create activity');
    }
  }

  async createActivityLog(
    body: {
      entity_id: number;
      activity: any;
      old_value?: any;
      new_value?: any;
      revert_log_id?: number;
    },
    queryRunner?: any,
  ) {
    console.time('Activity log');
    try {
      let revert, attributes_lists_object;
      let { entity_id, activity, old_value, new_value, revert_log_id } = body;
      let modified_old_values;

      if (activity.activity_type == 'create') {
        (revert = null), (revert_log_id = null);
      } else {
        (revert = false), revert_log_id ? revert_log_id : null;
      }

      attributes_lists_object = await new_value?.attributes_list?.reduce(
        (acc, attr) => {
          acc[attr.attribute_code] = attr.value;
          return acc;
        },
        {},
      );

      if (attributes_lists_object !== undefined) {
        new_value['attributes_list'] = await attributes_lists_object;
      }

      modified_old_values = await this.compareAndPopulateLogValues(
        new_value,
        old_value,
      );

      let activityLogData = {
        entity_id,
        activity,
        old_value:
          old_value === null ? null : JSON.stringify(modified_old_values),
        new_value: new_value === null ? null : JSON.stringify(new_value),
        revert,
        revert_log_id,
      };
      let saveActivityLogs;
      // NOTE: Here queryRunner is an instance which maintains the transaction
      // so if it is here then transaction is carried out otherwise products are saved normally without the reference of any transaction
      if (queryRunner) {
        const addActivityLog = await queryRunner.manager.create(
          ActivityLogs,
          activityLogData,
        );
        saveActivityLogs = await queryRunner.manager.save(
          ActivityLogs,
          addActivityLog,
        );
      } else {
        const addActivityLog =
          this.activityLogsRepository.create(activityLogData);
        saveActivityLogs =
          await this.activityLogsRepository.save(addActivityLog);
      }

      console.timeEnd('Activity log');

      // let addActivityLog = this.activityLogsRepository.create(activityLogData);
      // let saveActivityLogs =
      //   await this.activityLogsRepository.save(addActivityLog);
      return saveActivityLogs;
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to create activity logs', error);
      throw new InternalServerErrorException('Failed to create activity logs');
    }
  }

  async createEventOutbox(
    body: {
      entity_id: number;
      activity_logs: any;
      entity_type: EntityType;
    },
    queryRunner?: any,
  ) {
    console.time('Outbox table');
    try {
      let { entity_id, activity_logs, entity_type } = body;

      let eventOutboxData = {
        entity_id,
        activity_logs,
        entity_type,
        is_published: false,
        received_by_es: false,
        process_count: 0,
      };
      let saveEventsOutbox;
      // NOTE: Here queryRunner is an instance which maintains the transaction
      // so if it is here then transaction is carried out otherwise products are saved normally without the reference of any transaction
      if (queryRunner) {
        const addEventsOutbox = await queryRunner.manager.create(
          EventsOutbox,
          eventOutboxData,
        );
        saveEventsOutbox = await queryRunner.manager.save(
          EventsOutbox,
          addEventsOutbox,
        );
      } else {
        let addEventsOutbox =
          this.eventsOutboxRepository.create(eventOutboxData);
        saveEventsOutbox =
          await this.eventsOutboxRepository.save(addEventsOutbox);
      }

      console.timeEnd('Outbox table');

      // let addEventsOutbox = this.eventsOutboxRepository.create(eventOutboxData);
      // let saveEventsOutbox =
      //   await this.eventsOutboxRepository.save(addEventsOutbox);
      return saveEventsOutbox;
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to add data in outbox table', error);
      throw new InternalServerErrorException(
        'Failed to add data in outbox table',
      );
    }
  }

  async generateProductLogs(product_id: number, pagination: any) {
    try {
      const productExists = await this.catalogProductRepository.findOne({
        where: { id: product_id },
      });

      if (!productExists) {
        throw new BadRequestException('Requested product donot exists');
      }

      let activityLogsQuery: any = {
        relations: ['activity'],
        where: {
          entity_id: product_id,
          activity: {
            entity: EntityTypeEnum.PRODUCT,
          },
        },
        order: { id: 'DESC' },
      };

      activityLogsQuery['skip'] =
        ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
      activityLogsQuery['take'] = pagination?.size
        ? pagination?.size
        : env.sqlQueryResultsize;

      const searchedActivityLogs =
        await this.activityLogsRepository.findAndCount({
          ...activityLogsQuery,
        });

      if (!searchedActivityLogs) {
        return 'No logs available';
      }

      let logsData = searchedActivityLogs[0];
      let logsCount = searchedActivityLogs[1];

      let transformedData = await this.mapperForProductActivityLogs(logsData);

      return {
        item_count: logsCount,
        pages_count: Math.ceil(logsCount / env.sqlQueryResultsize),
        page_no: pagination?.page,
        page_size: pagination?.size || env.sqlQueryResultsize,
        data: transformedData,
      };
    } catch (error) {
      console.log(error);
      this.logger.error('Failed to retieve product logs', error);
      throw new InternalServerErrorException(
        'Failed to get logs for the requested product',
      );
    }
  }

  private async mapperForProductActivityLogs(logs: any[]) {
    try {
      let mappedData = await Promise.all(
        logs.map(async (e) => {
          let changes = await this.addAttributeWiseChangesInLogs(
            await JSON.parse(e.old_value),
            await JSON.parse(e.new_value),
          );
          let data = {
            id: e.id,
            created_at: e.created_at,
            updated_at: e.updated_at,
            type_id: e.type_id,
            admin_identifier: e.activity.user,
            activity_type: e.activity.activity_type,
            revert: e.revert,
            revert_log_id: e.revert_log_id,
            user_meta_info: JSON.parse(e.activity.user_meta_info) || null,
            old_value: e.old_value,
            new_value: e.new_value,
            changes: changes,
          };
          return data;
        }),
      );

      return mappedData;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to map logs data in correct format', err);
      throw new BadRequestException(
        'Failed to map logs data in correct format',
      );
    }
  }

  private async addAttributeWiseChangesInLogs(oldObj, newObj) {
    let changesObj = {};

    for (let key in newObj) {
      if (newObj.hasOwnProperty(key)) {
        if (Array.isArray(newObj[key])) {
          if (
            !Array.isArray(oldObj[key]) ||
            JSON.stringify(oldObj[key]) !== JSON.stringify(newObj[key])
          ) {
            changesObj[key] = {
              old_value: oldObj[key] || [],
              new_value: newObj[key],
            };
          }
        } else if (typeof newObj[key] === 'object' && newObj[key] !== null) {
          changesObj[key] = await this.addAttributeWiseChangesInLogs(
            oldObj[key] || {},
            newObj[key],
          );
        } else {
          if (oldObj[key] !== newObj[key]) {
            changesObj[key] = {
              old_value: oldObj[key] !== undefined ? oldObj[key] : null,
              new_value: newObj[key],
            };
          }
        }
      }
    }

    // Cleanup nested changes if they are empty
    for (let key in changesObj) {
      if (
        typeof changesObj[key] === 'object' &&
        Object.keys(changesObj[key]).length === 0
      ) {
        delete changesObj[key];
      }
    }

    return changesObj;
  }

  private async compareAndPopulateLogValues(newObj, oldObj) {
    let result = {};

    try {
      for (const key in newObj) {
        if (newObj.hasOwnProperty(key)) {
          if (
            typeof newObj[key] === 'object' &&
            newObj[key] !== null &&
            !Array.isArray(newObj[key])
          ) {
            // If it's a nested object, recurse
            result[key] = await this.compareAndPopulateLogValues(
              newObj[key],
              oldObj[key] || {},
            );
          } else {
            // If key exists in oldObj, take the value from oldObj, otherwise set to null
            result[key] = oldObj?.hasOwnProperty(key) ? oldObj[key] : null;
          }
        }
      }

      return result;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to populate old and new log values', err);
      throw new Error('Failed to populate old and new log values');
    }
  }

  async revertActivityLog(id: number, headers: any) {
    try {
      let user_meta_info = {};
      if (headers?.platform) user_meta_info['platform'] = headers.platform;
      if (headers?.user_agent)
        user_meta_info['user_agent'] = headers.user_agent;

      let user_data =
        Object.keys(user_meta_info).length === 0
          ? null
          : JSON.stringify(user_meta_info);
      let existingActivityLog = await this.activityLogsRepository.findOne({
        where: { id, revert: false },
        relations: ['activity'],
      });

      if (
        !existingActivityLog ||
        [
          'product_media',
          'viniculum_inventory_update',
          'bulk_attributes_import',
          'bulk_sku_update',
        ].includes(existingActivityLog.activity.activity_type)
      ) {
        throw new BadRequestException(
          'Invalid log id or this log is irreversable',
        );
      }

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'revert',
        user_meta_info: user_data,
      };

      let activityData = await this.createActivity(createActivityBody);

      let idNumber = Number(existingActivityLog.entity_id);

      let oldValues = JSON.parse(existingActivityLog.old_value);

      if (oldValues.attributes_list !== undefined) {
        let listArray = Object.entries(oldValues.attributes_list)
          .map(([key, value]) => ({
            attribute_code: key,
            value: value,
          }))
          .filter((item) => item.attribute_code !== 'url_key');

        oldValues.attributes_list = listArray;
      }

      let attributes_list_data: any = {
        ...oldValues,
        updated_by_action_details: {
          action_type: 'revert',
          activity: activityData,
          revert_log_id: existingActivityLog.id,
        },
      };

      await this.updateCatalogProduct(attributes_list_data, idNumber);
      const updateRevertStatus = await this.activityLogsRepository.update(
        { id },
        { revert: true },
      );
      return { message: 'Revert changes completed' };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to revert activity log', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to revert this activity log',
        );
      }
    }
  }

  async elsaticSearchCronJob() {
    try {
      const results = await this.eventsOutboxRepository
        .createQueryBuilder('events_outbox')
        .select('GROUP_CONCAT(events_outbox.id)', 'event_id')
        .addSelect('events_outbox.entity_id')
        .where(
          '(events_outbox.is_published = :isPublished OR events_outbox.received_by_es = :receivedByEs)',
          { isPublished: 0, receivedByEs: 0 },
        )
        .andWhere('events_outbox.process_count < :processCount', {
          processCount: env.outbox_api_constants.max_process_count,
        })
        .andWhere(
          'events_outbox.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)',
        )
        .groupBy('events_outbox.entity_id')
        .orderBy('events_outbox.id', 'DESC')
        // .orderBy('events_outbox.created_at', 'ASC')
        .limit(env.outbox_api_constants.products_count)
        .getRawMany();

      if (results.length == 0) {
        return { message: 'Now new events found' };
      }

      const productIds = results.map((e) => e.events_outbox_entity_id);
      const productDetails = await this.getProductsByIds(
        productIds,
        null,
        null,
        null,
      );

      console.log('Cronjob started via eventbus scheduler');

      for await (let products of productDetails.items) {
        let eventsFromResults = results.find(
          (event) => event.events_outbox_entity_id === products.id,
        );
        let eventIdArray = eventsFromResults.event_id.split(',').map(Number);

        await this.eventsOutboxRepository
          .createQueryBuilder()
          .update()
          .set({ is_published: true, process_count: () => 'process_count + 1' })
          .where('id IN (:...ids)', { ids: eventIdArray })
          .execute();

        //API TO sync data with elasticsearch
        // let sendData = await this.syncProductwithElasticSearch(
        //   products,
        //   eventIdArray,
        // );

        //API to sync data with mongo
        await this.syncProductwithMongo(products, eventIdArray);
      }
      return results;
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to push events in event bus for product sync',
        err,
      );
      throw new InternalServerErrorException(
        'Failed to push events in Eventbus',
      );
    }
  }

  async updateOutboxTable(outbox_table_ids: number[]) {
    try {
      let updateData = await this.eventsOutboxRepository
        .createQueryBuilder()
        .update()
        .set({ received_by_es: true })
        .where('id IN (:...ids)', { ids: outbox_table_ids })
        .execute();

      return { message: 'Outbox Table updated for requested product' };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to update outbox table logs', err);
      throw new InternalServerErrorException('Failed to update outbox logs');
    }
  }

  //NOTE: UNCOMMENT THIS CODE TO RUN CRONJOB : WE WILL DO THIS ON PRODUCTION
  // @Cron('*/5 * * * *')
  // async mongoDbCronJob() {
  //   try {
  //     const results = await this.eventsOutboxRepository
  //       .createQueryBuilder('events_outbox')
  //       .select('GROUP_CONCAT(events_outbox.id)', 'event_id')
  //       .addSelect('events_outbox.entity_id')
  //       .where(
  //         '(events_outbox.is_published = :isPublished OR events_outbox.received_by_es = :receivedByEs)',
  //         { isPublished: 0, receivedByEs: 0 },
  //       )
  //       .andWhere('events_outbox.process_count < :processCount', {
  //         processCount: env.outbox_api_constants.max_process_count,
  //       })
  //       .andWhere(
  //         'events_outbox.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)',
  //       )
  //       .groupBy('events_outbox.entity_id')
  //       .orderBy('events_outbox.id', 'DESC')
  //       .limit(env.outbox_api_constants.products_count)
  //       .getRawMany();

  //     if (results.length == 0) {
  //       return { message: 'Now new events found' };
  //     }

  //     console.log('Cronjob Started');

  //     const productIds = results.map((e) => e.events_outbox_entity_id);
  //     const productDetails = await this.getProductsByIds(
  //       productIds,
  //       null,
  //       null,
  //       null,
  //     );

  //     for await (let products of productDetails.items) {
  //       let eventsFromResults = results.find(
  //         (event) => event.events_outbox_entity_id === products.id,
  //       );
  //       let eventIdArray = eventsFromResults.event_id.split(',').map(Number);

  //       await this.eventsOutboxRepository
  //         .createQueryBuilder()
  //         .update()
  //         .set({ is_published: true, process_count: () => 'process_count + 1' })
  //         .where('id IN (:...ids)', { ids: eventIdArray })
  //         .execute();

  //       //API TO sync data with elasticsearch
  //       // let sendData = await this.syncProductwithElasticSearch(
  //       //   products,
  //       //   eventIdArray,
  //       // );

  //       console.log('Product pushed into event bus');
  //       //API to sync data with mongo
  //       await this.syncProductwithMongo(products, eventIdArray);
  //     }
  //     return results;
  //   } catch (err) {
  //     console.log(err);
  //     this.logger.error(
  //       'Failed to push events in event bus for product sync',
  //       err,
  //     );
  //     throw new InternalServerErrorException(
  //       'Failed to push events in Eventbus',
  //     );
  //   }
  // }

  async viniculumInventoryUpdate(sku, inventoryItem) {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      let { qty, is_in_stock } = inventoryItem;
      let productExists = await this.catalogProductRepository.findOne({
        where: { sku },
      });

      if (!productExists) {
        throw new BadRequestException('Invalid sku');
      }

      if (productExists.type_id === 'grouped') {
        return { message: 'Inventory details updated successfully' };
      }

      let createActivityBody = {
        user: 'Viniculum Inventory API',
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'viniculum_inventory_update',
        user_meta_info: { user: 'Viniculum' },
      };

      let activityData = await this.createActivity(createActivityBody);

      const currentInventoryValues = await queryRunner.manager.findOne(
        InventoryAttributes,
        { where: { product: { id: productExists.id } } },
      );

      await queryRunner.manager.update(
        InventoryAttributes,
        { product: { id: productExists.id } },
        { qty, is_in_stock },
      );

      // Determine the correct value for is_in_stock
      const adjustedIsInStock =
        currentInventoryValues?.backorders ||
        qty > currentInventoryValues?.min_sale_qty;

      if (is_in_stock !== adjustedIsInStock) {
        await queryRunner.manager.update(
          InventoryAttributes,
          { product: productExists },
          { is_in_stock: adjustedIsInStock },
        );
      }

      const newInventoryValues = await queryRunner.manager.findOne(
        InventoryAttributes,
        { where: { product: { id: productExists.id } } },
      );

      let createActivityLogsBody = {
        entity_id: productExists.id,
        old_value: { inventory_attributes: currentInventoryValues },
        new_value: { inventory_attributes: newInventoryValues },
        revert_log_id: null,
      };

      let activityLogsData = await this.createActivityLog(
        {
          ...createActivityLogsBody,
          activity: activityData,
        },
        queryRunner,
      );

      await this.createEventOutbox(
        {
          entity_id: productExists.id,
          entity_type: EntityType.PRODUCT,
          activity_logs: activityLogsData,
        },
        queryRunner,
      );

      await queryRunner.commitTransaction();

      if (is_in_stock === true) {
        await this.notifyCustomerAboutStockAvailability(productExists.id);
      }

      return { message: 'Inventory details updated successfully' };
    } catch (error) {
      console.log(error);
      this.logger.error(
        'Failed to update inventory changes from viniculum api',
        error,
      );
      await queryRunner.rollbackTransaction();
      if (error.status === 400) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          'Failed to update catalog product',
        );
      }
    } finally {
      await queryRunner.release();
    }
  }

  // COMMENTED BECAUSE IT IS CURRENTLY BEING USED IN EXTERNAL API HELPER
  // private async updateProductAndSkuInViniculum(product: any) {
  //   try {
  //     let productDetails: any = product.items;

  //     const url = env.viniculum.update_product_url;
  //     const headers = {
  //       ApiKey: env.viniculum.api_key,
  //       ApiOwner: env.viniculum.owner,
  //       'Content-Type': 'application/json',
  //     };
  //     let slicedShortName = productDetails.attributes_list.name.slice(0, 10);
  //     const data = {
  //       request: [
  //         {
  //           skuCode: productDetails.sku,
  //           skuName: productDetails.attributes_list.name,
  //           skuShortName: slicedShortName,
  //           mrp: productDetails.attributes_list.price,
  //           salePrice: productDetails.attributes_list.special_price,
  //           taxCategory: productDetails.attributes_list.hsn_code,
  //         },
  //       ],
  //     };

  //     const response = await axios.post(url, data, {
  //       headers,
  //     });
  //     // console.log(response.data, 'VINICULUM');
  //   } catch (err) {
  //     console.log(err);
  //     throw new InternalServerErrorException(
  //       'Failed to update product in viniculum',
  //     );
  //   }
  // }

  private async processStringAttributes(
    queryRunner,
    createdProduct,
    stringAttributes,
    attributeListFromDb,
    action,
  ) {
    console.time('string save');
    try {
      const ids = stringAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.stringAttributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of stringAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );

          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new StringAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of stringAttributes) {
          const newAttribute = new StringAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }
      // console.time('bulk string save');
      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });
      // console.timeEnd('bulk string save');

      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(StringAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(StringAttributeValues, entitiesToInsert);
      }

      console.timeEnd('string save');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to add attributes in string values table', err);
      throw new InternalServerErrorException(
        'Failed to add attributes in string value tables',
      );
    }
    // console.time('string save');
  }

  private async processIntAttributes(
    queryRunner,
    createdProduct,
    intAttributes,
    attributeListFromDb,
    action,
  ) {
    try {
      console.time('int save');

      const ids = intAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.intAttributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of intAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );

          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new IntegerAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of intAttributes) {
          const newAttribute = new IntegerAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });
      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(IntegerAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(IntegerAttributeValues, entitiesToInsert);
      }
      console.timeEnd('int save');
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to add attributes in integer values table',
        err,
      );
      throw new InternalServerErrorException(
        'Failed to add attributes in integer value tables',
      );
    }
  }

  private async processTextAttributes(
    queryRunner,
    createdProduct,
    textAttributes,
    attributeListFromDb,
    action,
  ) {
    try {
      console.time('text save');
      const ids = textAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.textAttributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of textAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );
          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new TextAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of textAttributes) {
          const newAttribute = new TextAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });

      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(TextAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(TextAttributeValues, entitiesToInsert);
      }
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to add attributes in text values table', err);
      throw new InternalServerErrorException(
        'Failed to add attributes in text value tables',
      );
    }
    console.timeEnd('text save');
  }

  private async processDateAttributes(
    queryRunner,
    createdProduct,
    dateAttributes,
    attributeListFromDb,
    action,
  ) {
    console.time('save date');
    try {
      const ids = dateAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.dateAttributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of dateAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );

          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new DateAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of dateAttributes) {
          const newAttribute = new DateAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });

      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(DateAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(DateAttributeValues, entitiesToInsert);
      }
      console.timeEnd('save date');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to add attributes in date values table', err);
      throw new InternalServerErrorException(
        'Failed to add attributes in date value tables',
      );
    }
  }

  private async processBooleanAttributes(
    queryRunner,
    createdProduct,
    booleanAttributes,
    attributeListFromDb,
    action,
  ) {
    console.time('save boolean');
    try {
      const ids = booleanAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.booleanAttributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of booleanAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );

          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new BooleanAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of booleanAttributes) {
          const newAttribute = new BooleanAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });

      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(BooleanAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(BooleanAttributeValues, entitiesToInsert);
      }
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to add attributes in boolean values table',
        err,
      );
      throw new InternalServerErrorException(
        'Failed to add attributes in boolean value tables',
      );
    }

    console.timeEnd('save boolean');
  }

  private async processDecimalAttributes(
    queryRunner,
    createdProduct,
    decimalAttributes,
    attributeListFromDb,
    action,
  ) {
    console.time('deciaml save');
    try {
      const ids = decimalAttributes.map((e) => e.attributeData.id);
      let existingAttributes, existingAttributeMap;
      const entitiesToUpdate = [];
      const entitiesToInsert = [];

      if (action == SavingAction.UPDATE) {
        existingAttributes = await this.decimalatributeValuesFindMethod({
          relations: ['product', 'attribute'],
          where: {
            product: { id: createdProduct.id },
            attribute: In(ids),
          },
        });

        // Create a map of existing attributes for efficient lookup
        existingAttributeMap = new Map(
          existingAttributes.map((attr) => [attr.attribute.id, attr]),
        );

        for await (const e of decimalAttributes) {
          const existingAttribute = existingAttributeMap.get(
            e.attributeData.id,
          );

          if (existingAttribute) {
            const attributeName = attributeListFromDb.find(
              (obj) => obj['id'] === e.attributeData.id,
            );

            if (attributeName.is_editable) {
              existingAttribute.value = e.value;
              entitiesToUpdate.push(existingAttribute);
            }
          } else {
            const newAttribute = new DecimalAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = e.value;
            newAttribute.product = createdProduct;
            entitiesToInsert.push(newAttribute);
          }
        }
      } else if (action == SavingAction.CREATE) {
        for await (const e of decimalAttributes) {
          const newAttribute = new DecimalAttributeValues();
          newAttribute.attribute = e.attributeData;
          newAttribute.value = e.value;
          newAttribute.product = createdProduct;
          entitiesToInsert.push(newAttribute);
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToUpdate.length > 0) {
      //     await manager.save(entitiesToUpdate);
      //   }
      //   if (entitiesToInsert.length > 0) {
      //     await manager.save(entitiesToInsert);
      //   }
      // });

      if (entitiesToUpdate.length > 0) {
        queryRunner.manager.save(DecimalAttributeValues, entitiesToUpdate);
      }
      if (entitiesToInsert.length > 0) {
        queryRunner.manager.save(DecimalAttributeValues, entitiesToInsert);
      }
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to add attributes in decimal values table',
        err,
      );
      throw new InternalServerErrorException(
        'Failed to add attributes in decimal value tables',
      );
    }
    console.timeEnd('deciaml save');
  }

  private async processIntWithOptionsAttributes(
    queryRunner,
    createdProduct,
    intWithOptionsAttributes,
  ) {
    console.time('save options att');
    try {
      const attributeOptionsIds = intWithOptionsAttributes.map(
        (e) => e.attributeData.id,
      );

      const findAttributeOptionsData =
        await this.productAttributesOptionsFindMethod({
          relations: ['attribute'],
          where: {
            attribute: { id: In(attributeOptionsIds) },
          },
        });

      // Create a map of attribute codes to option ids for efficient lookup
      const attributeOptionMap = new Map();
      findAttributeOptionsData.forEach((option) => {
        const attributeCode = option.attribute.code;
        if (!attributeOptionMap.has(attributeCode)) {
          attributeOptionMap.set(attributeCode, new Map());
        }
        attributeOptionMap.get(attributeCode).set(option.id, option);
      });

      // Arrays to accumulate entities for bulk operations
      const entitiesToSave = [];

      for await (const e of intWithOptionsAttributes) {
        const attributeCode = e.attributeData.code;
        const optionId = Number(e.value);
        const matchingOption = attributeOptionMap
          .get(attributeCode)
          ?.get(optionId);

        if (matchingOption) {
          // Check if the row already exists in the integer attribute values table
          const existingRow = await this.intAttributeValuesFindOneMethod({
            relations: ['product', 'attribute'],
            where: {
              attribute: { id: e.attributeData.id },
              product: { id: createdProduct.id },
            },
          });

          if (existingRow) {
            // If the row already exists, update it with the new value
            existingRow.value = matchingOption.id;
            entitiesToSave.push(existingRow);
          } else {
            // If the row does not exist, create a new one
            const newAttribute = new IntegerAttributeValues();
            newAttribute.attribute = e.attributeData;
            newAttribute.value = matchingOption.id;
            newAttribute.product = createdProduct;
            entitiesToSave.push(newAttribute);
          }
        }
      }

      // Perform bulk operations inside a transaction
      // await queryRunner.manager.transaction(async (manager) => {
      //   if (entitiesToSave.length > 0) {
      //     await manager.save(entitiesToSave);
      //   }
      // });

      if (entitiesToSave.length > 0) {
        queryRunner.manager.save(IntegerAttributeValues, entitiesToSave);
      }
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to add options attributes in integer values table',
        err,
      );
      throw new InternalServerErrorException(
        'Failed to add options attributes in integer value tables',
      );
    }
    console.timeEnd('save options att');
  }

  private async fetchTierPricesForGroupedProducts(product: any) {
    try {
      const associatedProductIds = await product.parentRelations
        .filter((relation) => relation.relation_type === 'associated')
        .map((relation) => relation.child.id);

      if (associatedProductIds.length < 1) {
        return [];
      }

      const childProductsData = await this.catalogProductFlatRepository.find({
        where: {
          product: { id: In(associatedProductIds) },
          type_id: Not('grouped'),
        },
        relations: ['product'],
        select: {
          id: true,
          special_price: true,
          product: { id: true, sku: true },
        },
      });

      const groupedProducts: GroupedProducts = childProductsData.reduce(
        (acc, product) => {
          const price = product.special_price;
          const productId = product.product.id;

          if (!acc[price]) {
            acc[price] = { price_group: price, child_ids: [] };
          }

          acc[price].child_ids.push(productId);

          return acc;
        },
        {} as GroupedProducts,
      );

      const representativeProductIds = Object.values(groupedProducts).map(
        (group) => group.child_ids[0],
      );

      const allTierPrices = await this.tierPricesRepository.find({
        where: { product: { id: In(representativeProductIds) } },
        relations: ['product'],
      });

      const groupTierPrice = Object.entries(groupedProducts).reduce(
        async (accPromise, [price, group]) => {
          const acc = await accPromise;
          const filteredTierPrices = await allTierPrices.filter(
            (e) => e.product.id === group.child_ids[0],
          );
          acc[price] = {
            price_group: group.price_group,
            tier_prices: filteredTierPrices.map((e) => ({
              qty: e.quantity,
              value: e.value,
              customer_group: e.customer_group,
              price_type: e.price_type,
            })),
            child_ids: group.child_ids,
          };

          return acc;
        },
        Promise.resolve({}),
      );

      return groupTierPrice;
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to fetch prices for grouped products', err);
      throw new InternalServerErrorException(
        'Failed to fetch prices for grouped products',
      );
    }
  }

  /**
   * Updates tier prices for groups of products based on their special prices
   * @param productId ID of the parent product
   * @param data Object containing tier prices grouped by special price values
   * @param headers User and metadata information
   * @returns Response message
   */
  async updateGroupTierPrice(
    productId: number,
    data: { [specialPriceKey: string]: { tier_prices: any[] } },
    headers: any,
  ) {
    try {
      // Check if there's data to process
      if (Object.keys(data).length === 0) {
        return 'No group prices to update';
      }

      // 1. Collect user metadata for activity logging
      const user_meta_info = this.extractUserMetaInfo(headers);
      const user_data = Object.keys(user_meta_info).length === 0 
        ? null 
        : JSON.stringify(user_meta_info);

      // 2. Create activity log entry
      const createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'group_tierprice_update',
        user_meta_info: user_data,
      };
      const activityData = await this.createActivity(createActivityBody);

      // 3. Find all associated products for the given parent product
      const associatedProducts = await this.findAssociatedProducts(productId);

      // 4. Group products by their special price values
      const groupedPriceGroups = this.groupProductsBySpecialPrice(associatedProducts);

      // 5. Update tier prices for each product in each price group
      await this.updateTierPricesForGroups(data, groupedPriceGroups, activityData);

      return { message: 'Group tier prices updated successfully' };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to update group tier prices', err);
      throw new InternalServerErrorException(
        'Failed to update group tier prices',
      );
    }
  }

  /**
   * Extracts user metadata from headers
   */
  private extractUserMetaInfo(headers: any): Record<string, any> {
    const userMetaInfo: Record<string, any> = {};
    if (headers?.platform) userMetaInfo['platform'] = headers.platform;
    if (headers?.user_agent) userMetaInfo['user_agent'] = headers.user_agent;
    return userMetaInfo;
  }

  /**
   * Finds all products associated with a parent product
   */
  private async findAssociatedProducts(productId: number) {
    return this.catalogProductRepository.find({
      relations: ['catalogProductFlatRelations', 'childRelations.parent'],
      where: {
        childRelations: {
          parent: { id: productId },
          relation_type: 'associated',
        },
      },
      select: {
        id: true,
        sku: true,
        type_id: true,
        parentRelations: {
          id: true,
          relation_type: true,
          position: true,
        },
        catalogProductFlatRelations: {
          id: true,
          special_price: true,
          product: {
            sku: true,
            id: true,
          },
        },
      },
    });
  }

  /**
   * Groups products by their special price value
   */
  private groupProductsBySpecialPrice(products: any[]): Record<string, { price_group: any, child_ids: number[] }> {
    return products.reduce((acc, product) => {
      const specialPrice = product.catalogProductFlatRelations.special_price;

      if (!acc[specialPrice]) {
        acc[specialPrice] = { price_group: specialPrice, child_ids: [] };
      }

      acc[specialPrice].child_ids.push(product.id);

      return acc;
    }, {});
  }

  /**
   * Updates tier prices for all products in each price group
   * Fixed the Promise handling issue by using Promise.all
   */
  private async updateTierPricesForGroups(
    data: { [specialPriceKey: string]: any },
    groupedPriceGroups: Record<string, { price_group: any, child_ids: number[] }>,
    activityData: any
  ) {
    // Process each special price group
    for (const specialPriceKey of Object.keys(data)) {
      const value = data[specialPriceKey];
      const productIds = groupedPriceGroups[specialPriceKey]?.child_ids;
      
      // Skip if no product IDs found for this special price
      if (!productIds || !Array.isArray(productIds)) {
        console.log(`No products found for special price: ${specialPriceKey}`);
        continue;
      }

      // Skip this price group if there are no tier prices to update
      if (!value.tier_prices || !Array.isArray(value.tier_prices) || value.tier_prices.length === 0) {
        // console.log(`Skipping price group ${specialPriceKey} - no tier prices to update`);
        continue;
      }
      
      // Create an array of promises only for products with tier prices to update
      const updatePromises = productIds.map(productId => {
        // Create a proper UpdateCatalogProductDto object
        const tierPricesData: any = {
          tier_prices: value.tier_prices,
          updated_by_action_details: {
            action_type: 'group_tierprice_update',
            activity: activityData,
          },
          // Required fields from UpdateCatalogProductDto with default values
          // status: undefined,                 // Optional in DTO
          // category_associated: undefined,     // Optional in DTO
          // product_links: undefined,          // Optional in DTO
          // inventory_details: undefined,      // Optional in DTO
          // attributes_list: undefined,        // Optional in DTO
        };
        
        // Return the promise (don't await here)
        return this.updateCatalogProduct(tierPricesData, productId);
      });

      // Wait for all updates in this group to complete
      await Promise.all(updatePromises);
    }
  }


  async modifyUrlRewritesV2(
    data: {
      entity_id: number;
      entity_type: any;
      request_path: string;
      target_path: string;
      old_request_path?: string;
    },
    queryRunner: QueryRunner,
  ) {
    try {
      console.time('MODIFY URL');
      console.log('input url data', data);
      let {
        entity_id,
        entity_type,
        request_path,
        target_path,
        old_request_path,
      } = data;

      let oldUrlRewrite = null,
        updatedUrlRewrite: UrlRewrites;

      if (old_request_path !== undefined) {
        oldUrlRewrite = await queryRunner.manager.findOne(UrlRewrites, {
          where: { request_path: old_request_path },
        });

        if (!oldUrlRewrite) {
          throw new NotFoundException(
            `URL with path ${old_request_path} does not exist.`,
          );
        }

        await queryRunner.manager.update(
          UrlRewrites,
          { id: oldUrlRewrite.id },
          { redirect_type: 301, target_path: request_path },
        );

        updatedUrlRewrite = {
          ...oldUrlRewrite,
          redirect_type: 301,
          target_path: request_path,
        };
      }

      const createdUrlRewrite = await queryRunner.manager.save(UrlRewrites, {
        redirect_type: 0,
        entity_id,
        entity_type,
        request_path,
        target_path,
      });

      console.timeEnd('MODIFY URL');

      return oldUrlRewrite
        ? { oldUrlRewrite, updatedUrlRewrite, createdUrlRewrite }
        : { createdUrlRewrite };
    } catch (err) {
      console.error(err);
      this.logger.error('Failed to update URL rewrites table', err);
      throw new InternalServerErrorException(
        'Failed to update URL rewrites table',
      );
    }
  }

  async fetchYoutubeVideoInfo(url: string) {
    try {
      const regExp = /[?&]v=([a-zA-Z0-9_-]{11})/;
      const match = url.match(regExp);
      let videoId;
      if (match && match[1]) {
        videoId = match[1];
      } else {
        // Handle invalid URL or no match
        throw new BadRequestException('Invalid YouTube URL');
      }

      const response =
        await this.externalApiHelper.fetchYoutubeVideoInfoHelper(videoId);
      return {
        title: response.items[0].snippet.title,
        description: response.items[0].snippet.description,
        default_thumbnail: response.items[0].snippet.thumbnails.default.url,
        medium_thumbnail: response.items[0].snippet.thumbnails.medium.url,
      };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to extract youtube url details', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to extract youtube url details',
        );
      }
    }
  }

  async saveProductsYoutubeVideoInfo(
    data: any,
    productId: number,
    headers: any,
  ) {
    try {
      const existingProduct = await this.catalogProductRepository.findOne({
        where: { id: productId },
      });
      if (!existingProduct) {
        throw new BadRequestException('Invalid Product Id');
      }
      let { url, title, description, thumbnail, db_video_id, position } = data;
      let mediaData;

      let createActivityBody = {
        user: headers.admin_identifier,
        entity: EntityTypeEnum.PRODUCT,
        activity_type: 'product_media',
        // user_meta_info: user_data
      };

      let fetchMediaData = await this.getProductsByIds(
        [productId],
        null,
        null,
        null,
      );
      let existingMediaData = fetchMediaData.items[0]?.media_gallery_entries;

      let activityData = await this.createActivity(createActivityBody);

      if (db_video_id) {
        let value = thumbnail;

        mediaData = await this.mediaGallaryVideoRepository.update(
          { id: db_video_id, product: { id: productId } },

          { position, title, description, value, url },
        );

        if (mediaData.affected == 0) {
          throw new BadRequestException('Invalid product or video id');
        }
      } else {
        const newMediaGallaryVideo = this.mediaGallaryVideoRepository.create({
          url,
          title,
          description,
          product: existingProduct,
          value: thumbnail,
          position: Number(`${existingMediaData.length + 1}`),
        });
        mediaData =
          await this.mediaGallaryVideoRepository.save(newMediaGallaryVideo);
      }

      let fetchMediaDataAfterChanges = await this.getProductsByIds(
        [productId],
        null,
        null,
        null,
      );
      let currentMediaData =
        fetchMediaDataAfterChanges.items[0]?.media_gallery_entries;

      let createActivityLogsBody = {
        entity_id: productId,
        old_value: { media_gallery_entries: existingMediaData },
        new_value: { media_gallery_entries: currentMediaData },
      };

      let activityLogsData = await this.createActivityLog({
        ...createActivityLogsBody,
        activity: activityData,
      });

      await this.createEventOutbox({
        entity_id: productId,
        entity_type: EntityType.PRODUCT,
        activity_logs: activityLogsData,
      });

      return { message: 'Video uploaded successfully' };
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to save media details for products video', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to save media details for products video',
        );
      }
    }
  }




  private async notifyCustomerAboutStockAvailability(productId) {
    try {
      const existingAlerts = await this.productStockAlertRepository.find({
        relations: ['product.catalogProductFlatRelations'],
        where: {
          product: productId,
          notification_status: false,
        },
        select: {
          id: true,
          customer_name: true,
          customer_email: true,
          notification_status: true,
          created_at: true,
          updated_at: true,
          product: {
            id: true,
            sku: true,
            catalogProductFlatRelations: {
              id: true,
              name: true,
              url_key: true,
            },
          },
        },
      });

      if (!existingAlerts || existingAlerts.length == 0) {
        return {
          message: 'No unsent notification alert is there for this product',
        };
      }

      existingAlerts.forEach(async (alertData) => {
        let data = {
          entity_value: alertData.customer_email,
          source: 'dentalkart_backend_admin',
          event_type: 'order_comment',
          subject: `Good News! Your Desired Product is Back in Stock!`,
          body: `Hi ${alertData.customer_name}, <br><br> We’re excited to inform you that the
          ${alertData.product.catalogProductFlatRelations.name} you’ve been waiting for is now back in stock! 
          Click the link below to make your purchase before it sells out again. <br><br>
          <a href="https://www.dentalkart.com/${alertData.product.catalogProductFlatRelations.url_key}.html">Click here</a> <br><br>
          Thank you for choosing Dentalkart! <br><br>
          Best regards,<br>
          The Dentalkart Team
          `,
          from: '<EMAIL>',
          'detail-type': 'email-notifications',
        };
        await this.externalApiHelper.sendStockAlertEventsInNotificationsEventBus(
          data,
        );
        await this.productStockAlertRepository.update(
          { id: alertData.id },
          { notification_status: true },
        );
      });

      return {
        message:
          'Customers have been notified about the availability of the product',
      };
    } catch (err) {
      console.log(err);
      this.logger.error(
        'Failed to notify customer about stock availability',
        err,
      );
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to notify customer about stock availability',
        );
      }
    }
  }


  async getUrlRewriteByIds(urlRewriteIds: number[]) {
    const urlRewrites = await this.urlRewritesRepository.find({
      where: { id: In(urlRewriteIds) },
    });
    return urlRewrites;
  }
}
